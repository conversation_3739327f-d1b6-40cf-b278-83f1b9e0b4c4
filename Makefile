ifneq (,$(wildcard .env))
    include .env
    export
endif

# Environment variables
export PYTHONPATH := $(shell pwd)
export POSTGRES_USER := postgres
export POSTGRES_PASSWORD := postgres
export POSTGRES_DB := businesslm
export POSTGRES_HOST := localhost
export POSTGRES_PORT := 5432

# Default prompt if none provided
DEFAULT_PROMPT := "Analyze the current market trends and provide a strategic recommendation for our business."

# Docker compose commands
.PHONY: up down
up:
	docker-compose up -d

down:
	docker-compose down

# Database commands
.PHONY: init-db reset-db
init-db:
	python backend/scripts/setup_pgvector_db.py

reset-db:
	python backend/scripts/database_migrations.py --reset

# Document management
.PHONY: upload-docs clear-docs
upload-docs:
	TOKENIZERS_PARALLELISM=false TRANSFORMERS_NO_ADVISORY_WARNINGS=1 JOBLIB_START_METHOD=thread JOBLIB_MULTIPROCESSING=0 python backend/scripts/upload_docs.py

clear-docs:
	python backend/scripts/load_knowledge_base_documents.py --clear

# Orchestration workflow commands
.PHONY: run-orchestration
run-orchestration:
	@if [ -z "$(prompt)" ]; then \
		echo "Error: Please provide a prompt with 'make run-orchestration prompt=\"your query\"'"; \
		exit 1; \
	else \
		echo "Running orchestration with prompt: $(prompt)"; \
		python -m cli orchestration run "$(prompt)"; \
	fi

# Testing commands
.PHONY: test-orchestration test-agents test-rag test-integration
test-orchestration:
	@if [ -z "$(prompt)" ]; then \
		echo "Using default prompt: $(DEFAULT_PROMPT)"; \
		python -m cli orchestration run "$(DEFAULT_PROMPT)"; \
	else \
		echo "Using custom prompt: $(prompt)"; \
		python -m cli orchestration run "$(prompt)"; \
	fi

test-agents:
	python backend/scripts/test_agents_implementation.py

test-rag:
	python backend/scripts/test_rag_pipeline.py

test-integration:
	python backend/scripts/system_integration_tests.py

# Visualization commands
.PHONY: visualize-orchestration
visualize-orchestration:
	python backend/scripts/visualize_orchestration.py

# Development setup
.PHONY: setup-dev validate-config
setup-dev:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt
	pre-commit install

validate-config:
	python backend/scripts/validate_config.py

# Help command
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  make up                       - Start the database and services"
	@echo "  make down                     - Stop all services"
	@echo "  make init-db                  - Initialize the database"
	@echo "  make reset-db                 - Reset the database"
	@echo "  make upload-docs              - Upload mock documents"
	@echo "  make clear-docs               - Clear all documents"
	@echo "  make run-orchestration prompt=\"your query\" - Run orchestration workflow with custom query"
	@echo "  make test-orchestration [prompt=\"your prompt\"] - Test orchestration workflow (uses CLI)"
	@echo "  make test-agents              - Test individual agents"
	@echo "  make test-rag                 - Test RAG functionality"
	@echo "  make test-integration         - Run system integration tests"
	@echo "  make visualize-orchestration  - Visualize the orchestration flow"
	@echo "  make validate-config          - Validate system configuration"
	@echo "  make setup-dev                - Setup development environment"