[project]
name = "businesslm-python-orchestration-poc"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.1",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.0",
    "pgvector>=0.2.0",
    "sentence-transformers>=4.1.0",
    "openai>=1.82.1",
    "anthropic>=0.52.1",
    "google-generativeai>=0.8.5",
    "greenlet>=3.2.2",
    "google-api-python-client>=2.170.0",
    "psutil>=7.0.0",
    "graphviz>=0.20.3",
    "tiktoken>=0.9.0",
    "tenacity>=9.1.2",
    "langgraph>=0.4.8",
    "langgraph-cli[inmem]>=0.3.1",
    "asyncpg>=0.30.0",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "ruff>=0.11.12",
]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["backend"]
