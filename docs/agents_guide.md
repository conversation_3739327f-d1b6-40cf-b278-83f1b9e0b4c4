# Agents Implementation Guide

## 📋 Overview

This guide provides comprehensive documentation for the multi-agent orchestration system implementation in `backend/agents/`. The system follows an **orchestration-first architecture** where agents collaborate through centralized planning and coordination.

---

## 🏗️ Architecture & Design Principles

### Core Principles

1. **Inheritance-Based Design**: All agents inherit from `BaseAgent` for consistent behavior
2. **Memory Integration**: Full traceability through shared memory system
3. **Strategic Intelligence**: RAG-powered context gathering for informed decisions
4. **Rule-Based Assignment**: Simple but extensible agent assignment logic
5. **Error Resilience**: Comprehensive fallback strategies and error handling
6. **Tool-Ready**: Prepared for future tool system integration

### Agent Hierarchy

```
BaseAgent (Abstract)
├── CoCEOAgent (Primary Orchestrator)
├── FinanceAgent (TODO - Domain Specialist)
└── MarketingAgent (TODO - Domain Specialist)
```

---

## 🧠 BaseAgent Implementation

### Purpose

Provides the foundational framework for all agents in the system, handling:

* Task execution lifecycle
* Memory integration
* LLM interaction patterns
* Error handling and recovery
* State management

### Key Methods

#### `async def run(task: Task, context: LangGraphState) -> str`

**The core agent execution method that all agents inherit.**

**Execution Flow:**

1. Validates inputs and task schema
2. Loads relevant memory context
3. Constructs prompts for the LLM
4. Handles LLM interaction with retries
5. Logs outputs and errors to memory
6. Injects results into orchestration state

### Memory Integration

* **Tags Used**: `llm_output`, `error`, `llm_failure`
* **Context Retrieval**: By agent and task ID
* **Traceability**: Each output and error is recorded

---

## 🎯 CoCEOAgent Implementation

### Purpose

The **primary orchestrator** responsible for:

* High-level planning and task decomposition
* Strategic decision making with RAG context
* Multi-agent coordination and assignment
* Direct tool usage for informed planning
* Plan evaluation and adjustment

---

### Core Components

#### Initialization

```python
def __init__(
    llm_adapter: LLMAdapter,
    memory: Optional[GlobalMemory] = None,
    planner: Optional[OrchestrationPlanner] = None,
    knowledge_base: Optional[KnowledgeBaseService] = None
)
```

* Automatically initializes `planner` and `knowledge_base` if not provided
* Registers known agents with capabilities
* Defines orchestration temperature and metadata

#### Agent Capability Registry

```python
self.register_agent("Finance", capabilities=[...], domain_keywords=[...])
```

Agents are registered with:

* Capabilities list
* Max concurrent task load
* Domain keywords for keyword-based assignment

---

### Primary Methods

#### `async def plan_execution(user_input, context) -> Tuple[List[Task], str]`

**Plans tasks from high-level input using the planner.**

Steps:

1. Initializes planner if needed
2. Logs the planning intent to memory
3. Calls `planner.generate_plan`
4. Enriches each task with strategic metadata
5. Logs final task list

Tags used: `plan_step`, `strategic`, `planning_result`

---

#### `async def gather_strategic_context(query, context) -> Dict[str, Any]`

**Multi-source information gathering**

Sources:

* RAG search results (if knowledge base is active)
* Strategic memories (`strategic`, `plan_step` tags)
* Timestamp metadata

---

#### `async def coordinate_agents(plan, available_agents, context) -> Dict[str, Any]`

**Assigns agents to tasks and determines execution order**

Features:

* Agent selection via keyword and capability match
* Dependency-aware execution ordering
* Memory logs of each assignment decision
* Logs coordination notes (e.g., bottlenecks, unused agents)

Returns:

```python
{
  "assignments": {"task_id": "agent_name"},
  "execution_order": [...],
  "dependencies": {...}
}
```

---

#### `async def make_strategic_decision(decision_context, options, context) -> Dict[str, Any]`

**Makes a high-level decision using LLM analysis.**

Process:

1. Gathers context (RAG + memory)
2. Builds a decision prompt
3. Gets structured LLM output (tries JSON parsing)
4. Fallbacks to first option if parsing fails
5. Logs decision rationale to memory

---

### Enrichment & Execution Helpers

#### `_enrich_plan_with_strategic_context(tasks, user_input, context)`

* Attaches RAG snippets (if available) to each task
* Adds `enriched_by`, `timestamp` metadata

#### `_determine_optimal_agent(task, agents, context)`

* Keyword-based agent selection fallback to CoCEO

#### `_calculate_execution_order(tasks)`

* Performs topological sort on tasks with dependencies

#### `_log_coordination_notes(plan, agents, context)`

* Internal logging only (unused agents, complexity, task distribution)

---

### Error Handling

#### Fallbacks

* Planning: generates fallback reasoning task
* Decision parsing: defaults to first option
* Agent assignment: fallback to CoCEO or first available

#### Logging

* Every failure includes task ID, context, and error string
* Strategic decisions and errors logged in memory

---

## 🔄 Integration Points

### With Orchestration System

* **State Management**: Fully integrated with `LangGraphState`
* **Planner**: Delegates to `OrchestrationPlanner`
* **Memory**: Writes/reads to `GlobalMemory`
* **Task Model**: Uses enriched `Task` model with metadata

---

## 📊 Key Metrics & Debugging

### Memory Tags Used

* `plan_step`, `strategic`, `coordination`, `decision`, `agent_assignment`

### Observability

* Each decision and assignment logged
* Coordination notes stored internally
* Memory retrieval supports debugging and replay

---

## ✅ Implementation Status

### Completed

* `BaseAgent` with core lifecycle
* `CoCEOAgent` with planning, coordination, and decisions
* Planner and memory integration
* Agent registration with domain matching
* Full error handling and fallback logic

### In Progress

* `FinanceAgent`, `MarketingAgent` (specialist agents)
* ToolRegistry integration (web search, email, etc.)
* LLM-based dynamic agent assignment
* Learning from outcomes and preference tracking
