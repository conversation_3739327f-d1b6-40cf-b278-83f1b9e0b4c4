# End-to-End Testing Guide

This guide explains how to use the end-to-end testing commands for the BusinessLM orchestration system.

## Prerequisites

1. Python 3.8+ installed
2. <PERSON><PERSON> and <PERSON><PERSON> Compose installed
3. PostgreSQL with pgvector extension (handled by <PERSON><PERSON>)

## Initial Setup

###
1. **Install Dependencies**:
   ```bash
   make setup-dev
   ```
   This will:
   - Install required Python packages
   - Install development dependencies
   - Set up pre-commit hooks

2. **Start the Database**:
   ```bash
   make up
   ```
   This starts the PostgreSQL database with pgvector extension in Docker.

3. **Initialize the Database**:
   ```bash
   make init-db
   ```
   This creates necessary tables and indexes.

## Document Management

### Uploading Documents

1. **Prepare Your Documents**:
   - Create a `mock_docs` directory in the project root
   - Add your documents (supported formats: .txt, .md, .pdf)
   - Documents will be automatically chunked and embedded

2. **Upload Documents**:
   ```bash
   make upload-docs
   ```
   This will:
   - Process all documents in the `mock_docs` directory
   - Split them into chunks
   - Generate embeddings
   - Store them in the database

3. **Clear Documents** (if needed):
   ```bash
   make clear-docs
   ```
   This removes all documents from the database.

## Running Tests

### Orchestration Testing

1. **Test with Default Prompt**:
   ```bash
   make test-orchestration
   ```
   This runs the orchestration with the default prompt:
   "Analyze the current market trends and provide a strategic recommendation for our business."

2. **Test with Custom Prompt and LLM Model**:
   ```bash
   make test-orchestration prompt="Your custom prompt here" llm_model="your_model_name"
   ```
   Example:
   ```bash
   make test-orchestration prompt="Analyze our Q4 financial performance and suggest improvements" llm_model="openai"
   ```

   The test will:
   - Process your prompt
   - Execute the orchestration flow
   - Generate a visualization
   - Display a summary of results
   - Show detailed task progress and agent interactions
   - Present the final system response from the LLM

### Individual Component Testing

1. **Test Agents**:
   ```bash
   make test-agents
   ```
   This tests individual agent functionality.

2. **Test RAG**:
   ```bash
   make test-rag
   ```
   This tests the RAG (Retrieval-Augmented Generation) functionality.

3. **Test Integration**:
   ```bash
   make test-integration
   ```
   This runs system integration tests.

## Visualization

### View Orchestration Flow

```bash
make visualize-orchestration
```
This generates and opens a visualization showing:
- Task execution flow
- Task dependencies
- Task status (completed/failed/pending)
- Performance metrics
- Agent interactions
- Task outputs

The visualization is saved in the `visualizations` directory with a timestamp.

## Understanding the Results

### Orchestration Summary

After running a test, you'll see a summary like:
```
Orchestration Summary:
Total Tasks: 10
Completed: 8
Failed: 2
Success Rate: 80.00%
Total Time: 45.23s
```

### Visualization Elements

The visualization includes:
- Step nodes (planning, task_assignment, etc.)
- Task nodes (color-coded by status)
- Dependency edges
- Performance metrics
- Agent interaction paths
- Task output details

## Troubleshooting

1. **Database Issues**:
   ```bash
   make down
   make up
   make init-db
   ```

2. **Document Processing Issues**:
   - Check document formats
   - Verify file permissions
   - Check log output

3. **Test Failures**:
   - Check the logs for detailed error messages
   - Verify database connection
   - Ensure documents are properly uploaded
   - Check agent configuration and permissions

## Advanced Usage

### Custom Document Processing

You can customize document processing with additional parameters:
```bash
python backend/scripts/upload_docs.py --chunk-size 500 --chunk-overlap 100
```

### Disable Visualization

To run tests without visualization:
```bash
python backend/scripts/test_orchestration.py --prompt "Your prompt" --no-visualize
```

## Best Practices

1. **Document Organization**:
   - Keep related documents together
   - Use meaningful filenames
   - Consider document size and complexity

2. **Prompt Design**:
   - Be specific and clear
   - Include necessary context
   - Consider task complexity
   - Test with different LLM models

3. **Testing Strategy**:
   - Start with simple prompts
   - Gradually increase complexity
   - Test edge cases
   - Monitor performance metrics
   - Verify agent interactions
   - Check task outputs

## Cleanup

To clean up the environment:
```bash
make down  # Stop the database
make clear-docs  # Clear documents
```

## Additional Resources

- Check the `visualizations` directory for past test results
- Review logs in the `logs` directory
- See `docs/` for additional documentation
- Check agent configuration in `backend/config/`
- Review task definitions in `backend/tasks/` 