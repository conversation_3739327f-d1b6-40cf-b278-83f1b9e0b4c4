# LLM System Architecture Documentation

## Overview

The LLM (Large Language Model) system provides a unified, modular interface for interacting with multiple LLM providers (OpenAI, Anthropic, Google Gemini) with intelligent model selection, environment-driven configuration, and seamless integration across the BusinessLM platform.

## Architecture Principles

- **Modularity**: Support for multiple providers with consistent interfaces
- **Smart Selection**: Automatic provider detection from model names
- **Environment-Driven**: Configuration through environment variables
- **Backward Compatibility**: Existing code continues to work unchanged
- **Graceful Fallbacks**: System remains functional when providers fail
- **Component-Specific**: Different models for different use cases (RAG, agents, orchestration)

## Core Components

### 1. Model Registry (`backend/llm/utils/model_registry.py`)

The model registry serves as the central database of available LLM models with their metadata.

#### Key Features:
- **Comprehensive Model Database**: 11+ models across OpenAI, Anthropic, Gemini
- **Provider Mapping**: Automatic detection of provider from model name
- **Capability Tracking**: Models tagged with capabilities (streaming, vision, code, reasoning)
- **Cost & Performance Metadata**: Context windows, token limits, pricing information
- **Validation**: Model existence and deprecation warnings
- **Recommendations**: Task-specific model suggestions

#### Core Classes:
```python
class ModelInfo:
    name: str                    # Model identifier
    provider: str               # Provider (openai, anthropic, gemini)
    max_tokens: int            # Maximum output tokens
    context_window: int        # Context window size
    capabilities: Set[ModelCapability]  # Model capabilities
    cost_per_1k_input: float   # Input token cost
    cost_per_1k_output: float  # Output token cost
    recommended_for: List[str] # Use case recommendations

class ModelRegistry:
    def get_provider_for_model(model_name: str) -> str
    def validate_model(model_name: str) -> Tuple[bool, str]
    def get_recommended_models(task_type: str) -> List[str]
    def get_models_by_capability(capability: ModelCapability) -> List[str]
```

#### Usage Examples:
```python
from llm.utils.model_registry import get_provider_for_model, validate_model

# Automatic provider detection
provider = get_provider_for_model("gpt-4o")  # Returns "openai"
provider = get_provider_for_model("claude-3-5-sonnet-20241022")  # Returns "anthropic"

# Model validation
is_valid, message = validate_model("gpt-4o")  # Returns (True, None)
is_valid, message = validate_model("invalid-model")  # Returns (False, "Unknown model")

# Get recommendations
rag_models = get_recommended_models("rag")  # Returns cost-effective models for RAG
code_models = get_recommended_models("code_generation")  # Returns code-optimized models
```

### 2. Configuration System (`backend/llm/utils/config.py`)

The configuration system implements a sophisticated cascade for model selection and LLM parameters.

#### Configuration Cascade:
1. **Explicit Parameters**: Directly provided in code
2. **Component-Specific Environment**: `RAG_LLM_MODEL`, `AGENT_LLM_MODEL`, etc.
3. **System Default Environment**: `LLM_DEFAULT_MODEL`
4. **Hardcoded Fallbacks**: `gpt-4o-mini`

#### Key Features:
- **Environment Integration**: Reads from `.env` and environment variables
- **Component Awareness**: Different defaults for RAG vs agents vs orchestration
- **Provider Detection**: Automatic provider resolution from model names
- **Validation**: Built-in model validation with helpful error messages

#### Core Class:
```python
class LLMConfig:
    def __init__(
        self,
        model: Optional[str] = None,           # Model name (auto-resolved if None)
        component_type: Optional[str] = None,  # "rag", "agent", "orchestration", "planning"
        temperature: Optional[float] = None,   # Sampling temperature
        max_tokens: Optional[int] = None,      # Maximum tokens to generate
        timeout: Optional[float] = None,       # Request timeout
        max_retries: Optional[int] = None,     # Maximum retry attempts
        streaming: Optional[bool] = None       # Enable streaming responses
    )
    
    def get_provider(self) -> Optional[str]    # Get provider for configured model
    def validate(self) -> Tuple[bool, str]     # Validate configuration
    def to_dict(self) -> dict                  # Export as dictionary
```

#### Usage Examples:
```python
from llm.utils.config import LLMConfig

# Component-specific configuration (uses RAG_LLM_MODEL if set)
rag_config = LLMConfig(component_type="rag", temperature=0.3)

# Explicit model override
config = LLMConfig(model="claude-3-5-sonnet-20241022", temperature=0.1)

# Environment defaults (uses LLM_DEFAULT_MODEL)
default_config = LLMConfig()

# Provider detection
provider = config.get_provider()  # Returns "anthropic" for Claude models
```

### 3. Smart Factory System (`backend/llm/utils/factory.py`)

The factory system provides multiple interfaces for creating LLM adapters with intelligent provider selection.

#### Factory Methods:

##### Traditional Provider-Based (Backward Compatible):
```python
def get_llm_adapter(provider: str, config: LLMConfig = None, fallback: bool = True) -> LLMAdapter
```

##### Smart Model-Based Selection:
```python
def get_llm_adapter_by_model(model_name: str, config: LLMConfig = None, fallback: bool = True) -> LLMAdapter
```

##### Unified Smart Interface:
```python
def get_llm_adapter_smart(
    model_name: Optional[str] = None,
    provider: Optional[str] = None,
    config: Optional[LLMConfig] = None,
    component_type: Optional[str] = None,
    fallback: bool = True
) -> LLMAdapter
```

#### Key Features:
- **Lazy Initialization**: Prevents circular import issues
- **Automatic Provider Detection**: Maps model names to providers
- **Graceful Fallbacks**: Falls back to available providers when requested provider fails
- **Validation Options**: Startup validation vs lazy validation
- **Multiple Interfaces**: Supports both explicit and smart selection

#### Usage Examples:
```python
from llm.utils.factory import get_llm_adapter_smart, get_llm_adapter_by_model

# Smart model selection (auto-detects provider)
adapter = get_llm_adapter_by_model("gpt-4o")
adapter = get_llm_adapter_by_model("claude-3-5-sonnet-20241022")

# Unified smart interface
adapter = get_llm_adapter_smart(model_name="gpt-4o")           # Model-based
adapter = get_llm_adapter_smart(provider="openai")            # Provider-based
adapter = get_llm_adapter_smart(component_type="rag")         # Component defaults
adapter = get_llm_adapter_smart()                             # Environment defaults

# Traditional (backward compatible)
adapter = get_llm_adapter(provider="openai")
```

### 4. Provider Adapters

#### OpenAI Adapter (`backend/llm/openai.py`)
- **Models**: GPT-4o, GPT-4o-mini, GPT-4-turbo-preview, GPT-3.5-turbo
- **Features**: Chat completion, streaming, function calling, vision (GPT-4o)
- **Token Counting**: tiktoken integration for accurate token counting

#### Anthropic Adapter (`backend/llm/anthropic.py`)
- **Models**: Claude-3.5-Sonnet, Claude-3-Haiku, Claude-3-Opus
- **Features**: Chat completion, streaming, large context windows (200K tokens)
- **Strengths**: Reasoning, analysis, code generation

#### Gemini Adapter (`backend/llm/gemini.py`)
- **Models**: Gemini-2.0-Flash, Gemini-2.5-Pro
- **Features**: Chat completion, streaming, vision, massive context (1M+ tokens)
- **Strengths**: Multimodal capabilities, cost-effectiveness

#### Mock Adapter (`backend/llm/mock.py`)
- **Purpose**: Testing, development, fallback
- **Features**: Simulates real adapter behavior without API calls
- **Always Available**: No external dependencies

## Integration with Other Components

### RAG System Integration

The RAG system integrates seamlessly with the LLM system through component-specific configuration:

```python
# RAG automatically uses RAG_LLM_MODEL if set, otherwise LLM_DEFAULT_MODEL
from rag.llm import get_rag_llm_adapter

rag_adapter = get_rag_llm_adapter()  # Uses smart factory internally
```

#### RAG-Specific Features:
- **Component Type**: `component_type="rag"` for RAG-optimized defaults
- **Cost Optimization**: Defaults to cost-effective models like `claude-3-haiku-20240307`
- **Smart Integration**: Uses `get_llm_adapter_smart` internally for provider detection

### Orchestration System Integration

The orchestration system can use different models for different tasks:

```python
# Different models for different orchestration tasks
planning_config = LLMConfig(component_type="planning")      # Uses PLANNING_LLM_MODEL
orchestration_config = LLMConfig(component_type="orchestration")  # Uses ORCHESTRATION_LLM_MODEL

# Create adapters for different purposes
planning_adapter = get_llm_adapter_smart(config=planning_config)
orchestration_adapter = get_llm_adapter_smart(config=orchestration_config)
```

### Agent System Integration

Agents can use specialized models optimized for their specific tasks:

```python
# Agent-specific configuration
agent_config = LLMConfig(
    component_type="agent",
    temperature=0.7,  # Higher creativity for agent interactions
    max_tokens=2000   # Longer responses for complex agent tasks
)

agent_adapter = get_llm_adapter_smart(config=agent_config)
```

## Environment Configuration

### Environment Variables

The system supports comprehensive environment-driven configuration:

```bash
# System-wide default
LLM_DEFAULT_MODEL=gpt-4o-mini

# Component-specific overrides
RAG_LLM_MODEL=claude-3-haiku-20240307        # Cost-effective for RAG
AGENT_LLM_MODEL=gpt-4o                       # High-capability for agents
ORCHESTRATION_LLM_MODEL=claude-3-5-sonnet-20241022  # Reasoning for orchestration
PLANNING_LLM_MODEL=gpt-4o                    # Planning and strategy

# LLM parameters
LLM_DEFAULT_TEMPERATURE=0.7
LLM_DEFAULT_MAX_TOKENS=1000
LLM_DEFAULT_TIMEOUT=30.0
LLM_DEFAULT_MAX_RETRIES=3
```

### Configuration Validation

The system validates configuration at startup:

```python
from backend.app.config import get_settings

settings = get_settings()
llm_errors = settings.validate_llm_models()  # Validates all configured models
```

## Error Handling & Fallbacks

### Graceful Degradation

The system implements multiple levels of fallback:

1. **Provider Fallback**: If OpenAI fails, try Anthropic, then Gemini, then Mock
2. **Model Fallback**: If specific model unavailable, use provider default
3. **Configuration Fallback**: If environment config invalid, use system defaults
4. **Import Fallback**: If modules can't be imported, use minimal implementations

### Error Types

- **Model Validation Errors**: Unknown or deprecated models
- **Provider Unavailability**: Missing API keys or network issues
- **Configuration Errors**: Invalid environment variables
- **Import Errors**: Missing dependencies or circular imports

## Testing & Validation

### Test Suite

The system includes comprehensive testing:

1. **Unit Tests**: Individual component functionality
2. **Integration Tests**: End-to-end system behavior
3. **Import Structure Tests**: Circular dependency detection
4. **Configuration Tests**: Environment variable handling
5. **Fallback Tests**: Error handling and graceful degradation

### Test Scripts

- `test_llm_configuration.py`: Core functionality testing
- `test_llm_integration.py`: Comprehensive integration testing
- `validate_import_structure.py`: Import structure validation
- `test_import_structure.py`: Circular dependency detection

## Performance Considerations

### Lazy Loading

- **Factory Initialization**: Adapters loaded on-demand
- **Model Registry**: Populated at first access
- **Provider Detection**: Cached after first lookup

### Memory Efficiency

- **Singleton Pattern**: Single factory instance
- **Minimal Imports**: Only load required adapters
- **Fallback Implementations**: Lightweight mock implementations

### Scalability

- **Async Support**: All adapters support async operations
- **Connection Pooling**: Efficient HTTP connection reuse
- **Rate Limiting**: Built-in retry logic with exponential backoff

## Future Extensibility

### Adding New Providers

1. Create new adapter class inheriting from `LLMAdapter`
2. Add models to `model_registry.py`
3. Register in factory `_adapters` mapping
4. Add availability check

### Adding New Models

1. Add `ModelInfo` entry to registry
2. Update provider adapter if needed
3. Add to recommended models for appropriate tasks

### Adding New Capabilities

1. Extend `ModelCapability` enum
2. Update model entries with new capabilities
3. Add capability-specific methods to adapters

## Best Practices

### For Developers

1. **Use Smart Factory**: Prefer `get_llm_adapter_smart()` for new code
2. **Component Types**: Always specify `component_type` for component-specific code
3. **Environment Config**: Use environment variables for deployment-specific settings
4. **Error Handling**: Always enable fallbacks unless strict provider control needed
5. **Testing**: Use mock adapter for unit tests

### For Deployment

1. **Environment Variables**: Set appropriate model defaults for each environment
2. **API Keys**: Ensure all required provider API keys are configured
3. **Monitoring**: Monitor model usage and costs
4. **Fallbacks**: Enable fallbacks in production for reliability
5. **Validation**: Run configuration validation at startup

## Troubleshooting

### Common Issues

1. **Import Errors**: Check Python path and module structure
2. **Model Not Found**: Verify model name in registry
3. **Provider Unavailable**: Check API keys and network connectivity
4. **Configuration Errors**: Validate environment variables
5. **Circular Imports**: Use lazy imports and factory patterns

### Debug Tools

- `validate_import_structure.py`: Check import health
- `test_llm_integration.py`: Verify system functionality
- Configuration validation in settings
- Comprehensive logging throughout system

This LLM system provides a robust, scalable foundation for AI-powered applications with intelligent model selection, comprehensive error handling, and seamless integration across the BusinessLM platform.
