"""
LLM Adapter Package

This package provides a unified interface for interacting with different LLM providers.

Required dependencies:
    - For OpenAI: uv add install openai
    - For Anthropic: uv add anthropic
    - For Google Gemini: uv add google-generativeai
    - For token counting: uv add tiktoken
    - For retry logic: uv add tenacity

Example usage:
    >>> adapter = get_llm_adapter("openai")
    >>> response = await adapter.chat([{"role": "user", "content": "Hello!"}])
    >>> print(response)  # response is a string

Streaming example:
    >>> adapter = get_llm_adapter("openai")
    >>> # When stream=True, the response is an async generator
    >>> async for chunk in adapter.chat([{"role": "user", "content": "Hello!"}], stream=True):
    >>>     print(chunk, end="", flush=True)
"""

# Import OpenAI
from .openai import (
    OpenAIAdapter,
    OPENAI_AVAILABLE,
    TIKTOKEN_AVAILABLE,
    TENACITY_AVAILABLE,  # Import tenacity availability flag
)

# Import Anthropic
from .anthropic import (
    AnthropicAdapter,
    ANTHROPIC_AVAILABLE,
)

# Import Gemini
from .gemini import (
    Gemini<PERSON>dapter,
    GEMINI_AVAILABLE,
)

# Import mock
from .mock import MockAdapter

# Import factory functions
from .utils.factory import (
    get_llm_adapter,
)

# Import utils
from .utils.config import LLMConfig
from .utils.base import ChatMessage, LLMAdapter

# Group SDK flags for easier environment debug/validation
LLM_DEPENDENCIES = {
    "openai": OPENAI_AVAILABLE,
    "anthropic": ANTHROPIC_AVAILABLE,
    "gemini": GEMINI_AVAILABLE,
    "tiktoken": TIKTOKEN_AVAILABLE,
    "tenacity": TENACITY_AVAILABLE
}


# Define list of all exported symbols
__all__ = [
    # from openai.py
    "OpenAIAdapter",
    "OPENAI_AVAILABLE",
    "TIKTOKEN_AVAILABLE",
    "TENACITY_AVAILABLE",

    # from anthropic.py
    "AnthropicAdapter",
    "ANTHROPIC_AVAILABLE",

    # from gemini.py
    "GeminiAdapter",
    "GEMINI_AVAILABLE",

    # from mock.py
    "MockAdapter",

    # from utils.factory
    "get_llm_adapter",

    # from utils.config
    "LLMConfig",

    # from utils.base
    "ChatMessage",
    "LLMAdapter",

    # LLM Dependencies
    "LLM_DEPENDENCIES",
]
