"""
Centralized configuration for LLM adapters, including timeout and retry settings.
"""
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class LLMConfig:
    """
    Configuration class for LLM adapters with environment-driven defaults.

    This class supports a configuration cascade:
    Environment Variables → Component Config → System Defaults → Provider Defaults
    """

    def __init__(
        self,
        model: Optional[str] = None,
        timeout: Optional[float] = None,
        max_retries: Optional[int] = None,
        min_backoff: Optional[int] = None,
        max_backoff: Optional[int] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        streaming: Optional[bool] = None,
        component_type: Optional[str] = None,
    ):
        """
        Initialize LLM configuration with environment-driven defaults.

        Args:
            model: Model name (if None, will use environment/system defaults)
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            min_backoff: Minimum backoff time for retries
            max_backoff: Maximum backoff time for retries
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            streaming: Whether to stream responses
            component_type: Component type for component-specific defaults
                          (e.g., "rag", "agent", "orchestration", "planning")
        """
        # Get system settings for defaults
        settings = self._get_settings()

        # Apply configuration cascade
        self.model = self._resolve_model(model, component_type, settings)
        self.timeout = timeout if timeout is not None else settings.LLM_DEFAULT_TIMEOUT
        self.max_retries = max_retries if max_retries is not None else settings.LLM_DEFAULT_MAX_RETRIES
        self.min_backoff = min_backoff if min_backoff is not None else 2
        self.max_backoff = max_backoff if max_backoff is not None else 10
        self.temperature = temperature if temperature is not None else settings.LLM_DEFAULT_TEMPERATURE
        self.max_tokens = max_tokens if max_tokens is not None else settings.LLM_DEFAULT_MAX_TOKENS
        self.streaming = streaming if streaming is not None else False

        # Store component type for reference
        self.component_type = component_type

        logger.debug(f"LLMConfig initialized: model={self.model}, component_type={component_type}")

    def _get_settings(self):
        """Get application settings, handling circular imports."""
        try:
            from ...app.config import get_settings
            return get_settings()
        except ImportError:
            # Fallback if settings not available
            logger.warning("Could not import settings, using hardcoded defaults")
            return self._get_fallback_settings()

    def _get_fallback_settings(self):
        """Provide fallback settings if main settings unavailable."""
        class FallbackSettings:
            LLM_DEFAULT_MODEL = "gpt-4o-mini"
            LLM_DEFAULT_TEMPERATURE = 0.7
            LLM_DEFAULT_MAX_TOKENS = 1000
            LLM_DEFAULT_MAX_RETRIES = 3
            LLM_DEFAULT_TIMEOUT = 30.0
            RAG_LLM_MODEL = None
            AGENT_LLM_MODEL = None
            ORCHESTRATION_LLM_MODEL = None
            PLANNING_LLM_MODEL = None

        return FallbackSettings()

    def _resolve_model(self, model: Optional[str], component_type: Optional[str], settings) -> str:
        """
        Resolve the model name using configuration cascade.

        Priority:
        1. Explicitly provided model
        2. Component-specific environment variable
        3. System default environment variable
        4. Hardcoded fallback
        """
        # 1. Use explicitly provided model
        if model:
            return model

        # 2. Use component-specific model if available
        if component_type:
            component_model = self._get_component_model(component_type, settings)
            if component_model:
                logger.debug(f"Using component-specific model for {component_type}: {component_model}")
                return component_model

        # 3. Use system default
        if hasattr(settings, 'LLM_DEFAULT_MODEL') and settings.LLM_DEFAULT_MODEL:
            logger.debug(f"Using system default model: {settings.LLM_DEFAULT_MODEL}")
            return settings.LLM_DEFAULT_MODEL

        # 4. Hardcoded fallback
        fallback_model = "gpt-4o-mini"
        logger.warning(f"No model configuration found, using fallback: {fallback_model}")
        return fallback_model

    def _get_component_model(self, component_type: str, settings) -> Optional[str]:
        """Get component-specific model from settings."""
        component_mapping = {
            "rag": getattr(settings, 'RAG_LLM_MODEL', None),
            "agent": getattr(settings, 'AGENT_LLM_MODEL', None),
            "orchestration": getattr(settings, 'ORCHESTRATION_LLM_MODEL', None),
            "planning": getattr(settings, 'PLANNING_LLM_MODEL', None),
        }

        return component_mapping.get(component_type.lower())

    def get_provider(self) -> Optional[str]:
        """Get the provider for the configured model."""
        try:
            from .model_registry import get_provider_for_model
            return get_provider_for_model(self.model)
        except ImportError:
            logger.warning("Could not import model registry for provider detection")
            return None

    def validate(self) -> tuple[bool, Optional[str]]:
        """
        Validate the configuration.

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            from .model_registry import validate_model
            return validate_model(self.model)
        except ImportError:
            logger.warning("Could not import model registry for validation")
            return True, None

    def to_dict(self) -> dict:
        """Convert configuration to dictionary."""
        return {
            "model": self.model,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "min_backoff": self.min_backoff,
            "max_backoff": self.max_backoff,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "streaming": self.streaming,
            "component_type": self.component_type,
        }

    def __repr__(self) -> str:
        """String representation of the configuration."""
        return f"LLMConfig(model='{self.model}', component_type='{self.component_type}')"