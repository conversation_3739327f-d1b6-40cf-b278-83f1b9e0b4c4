"""
LLM Factory

This module provides a factory for creating LLM adapters.
"""

import logging
from typing import Dict, Optional, Type

from .base import <PERSON><PERSON>dapter
from .config import LLMConfig
from ..openai import OpenAIAdapter, OPENAI_AVAILABLE
from ..anthropic import <PERSON>throp<PERSON><PERSON><PERSON>pter, ANTHROPIC_AVAILABLE
from ..gemini import <PERSON><PERSON><PERSON>pt<PERSON>, GEMINI_AVAILABLE
from ..mock import MockAdapter

logger = logging.getLogger(__name__)


class LLMFactory:
    """
    Factory for creating LLM adapters.

    This class provides a centralized way to create and configure LLM adapters,
    with support for different providers and configurations.
    """

    _adapters: Dict[str, Type[LLMAdapter]] = {
        "openai": OpenAIAdapter,
        "anthropic": AnthropicAdapter,
        "gemini": GeminiAdapter,
        "mock": MockAdapter
    }

    _availability_checks: Dict[str, bool] = {
        "openai": OPENAI_AVAILABLE,
        "anthropic": ANTHROPIC_AVAILABLE,
        "gemini": GEMINI_AVAILABLE,
        "mock": True  # Mock is always available
    }

    _fallback_order = ["mock", "openai", "anthropic", "gemini"]

    @classmethod
    def create(
        cls,
        provider: str,
        config: Optional[LLMConfig] = None,
        fallback: bool = True,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter.

        Args:
            provider: LLM provider name
            config: Optional configuration
            fallback: Whether to try fallback providers if the requested one fails
            **kwargs: Additional provider-specific arguments

        Returns:
            Configured LLM adapter

        Raises:
            ValueError: If provider is not supported and fallback is False
        """
        provider = provider.lower()

        # Try the requested provider first
        if provider in cls._adapters and cls._availability_checks.get(provider, False):
            try:
                adapter_class = cls._adapters[provider]
                logger.info(f"Creating {provider} adapter")
                return adapter_class(config=config, **kwargs)
            except Exception as e:
                if not fallback:
                    raise
                logger.warning(f"Failed to create {provider} adapter: {e}")

        # If fallback is enabled, try fallback providers
        if fallback:
            return cls._create_with_fallback(provider, config, **kwargs)

        # If no fallback and provider not available
        if provider not in cls._adapters:
            raise ValueError(f"Unsupported LLM provider: {provider}")
        else:
            raise ValueError(f"LLM provider {provider} is not available")

    @classmethod
    def _create_with_fallback(
        cls,
        original_provider: str,
        config: Optional[LLMConfig] = None,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter using fallback providers.

        Args:
            original_provider: The originally requested provider
            config: Optional configuration
            **kwargs: Additional provider-specific arguments

        Returns:
            Configured LLM adapter using the first available fallback

        Raises:
            ValueError: If no fallback providers are available
        """
        logger.warning(f"Trying fallback providers for {original_provider}")

        for fallback_provider in cls._fallback_order:
            if fallback_provider in cls._adapters and cls._availability_checks.get(fallback_provider, False):
                try:
                    adapter_class = cls._adapters[fallback_provider]
                    logger.info(f"Using fallback provider: {fallback_provider}")
                    return adapter_class(config=config, **kwargs)
                except Exception as e:
                    logger.warning(f"Fallback provider {fallback_provider} failed: {e}")
                    continue

        raise ValueError("No available LLM providers found")

    @classmethod
    def register_adapter(
        cls,
        name: str,
        adapter_class: Type[LLMAdapter],
        available: bool = True
    ) -> None:
        """
        Register a new LLM adapter.

        Args:
            name: Provider name
            adapter_class: Adapter class
            available: Whether the adapter is available
        """
        cls._adapters[name.lower()] = adapter_class
        cls._availability_checks[name.lower()] = available
        logger.info(f"Registered adapter: {name}")

    @classmethod
    def get_supported_providers(cls) -> list[str]:
        """Get list of supported LLM providers."""
        return list(cls._adapters.keys())

    @classmethod
    def get_available_providers(cls) -> list[str]:
        """Get list of available LLM providers."""
        return [name for name, available in cls._availability_checks.items() if available]


# Global factory instance
_llm_factory = LLMFactory()

# Registry alias for compatibility
ADAPTER_REGISTRY = _llm_factory._adapters


def get_llm_adapter(
    provider: str = "mock",
    config: Optional[LLMConfig] = None,
    fallback: bool = True,
    **kwargs
) -> LLMAdapter:
    """
    Get an LLM adapter based on the provider.

    This is the main factory function for creating LLM adapters with fallback support.

    Args:
        provider: LLM provider name (default: "mock")
        config: Optional LLM configuration
        fallback: Whether to enable fallback to other providers (default: True)
        **kwargs: Additional arguments passed to the adapter

    Returns:
        An initialized LLM adapter ready for use

    Raises:
        ValueError: If provider is not supported and fallback is False
    """
    return _llm_factory.create(
        provider=provider,
        config=config,
        fallback=fallback,
        **kwargs
    )


def get_llm_adapter_factory() -> LLMFactory:
    """
    Get the global LLM factory instance.

    Returns:
        The global LLMFactory instance
    """
    return _llm_factory


def register_adapter(
    name: str,
    adapter_class: Type[LLMAdapter],
    available: bool = True
) -> None:
    """
    Register a new LLM adapter with the global factory.

    Args:
        name: Provider name
        adapter_class: Adapter class
        available: Whether the adapter is available
    """
    _llm_factory.register_adapter(name, adapter_class, available)
