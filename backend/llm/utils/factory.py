"""
LLM Factory

This module provides a factory for creating LLM adapters with smart model selection.
"""

import logging
from typing import Dict, Optional, Type, Tuple

from .base import LLMAdapter
from .config import LLMConfig

logger = logging.getLogger(__name__)


class LLMFactory:
    """
    Factory for creating LLM adapters.

    This class provides a centralized way to create and configure LLM adapters,
    with support for different providers and configurations.
    """

    def __init__(self):
        """Initialize the factory with lazy imports to avoid circular dependencies."""
        self._adapters: Dict[str, Type[LLMAdapter]] = {}
        self._availability_checks: Dict[str, bool] = {}
        self._fallback_order = ["mock", "openai", "anthropic", "gemini"]
        self._initialized = False

    def _ensure_initialized(self):
        """Ensure adapters are loaded (lazy initialization)."""
        if self._initialized:
            return

        # Import adapters here to avoid circular imports
        try:
            from ..openai import OpenAIAdapter, OPENAI_AVAILABLE
            self._adapters["openai"] = OpenAIAdapter
            self._availability_checks["openai"] = OPENAI_AVAILABLE
        except ImportError:
            logger.warning("OpenAI adapter not available")
            self._availability_checks["openai"] = False

        try:
            from ..anthropic import AnthropicAdapter, ANTHROPIC_AVAILABLE
            self._adapters["anthropic"] = AnthropicAdapter
            self._availability_checks["anthropic"] = ANTHROPIC_AVAILABLE
        except ImportError:
            logger.warning("Anthropic adapter not available")
            self._availability_checks["anthropic"] = False

        try:
            from ..gemini import GeminiAdapter, GEMINI_AVAILABLE
            self._adapters["gemini"] = GeminiAdapter
            self._availability_checks["gemini"] = GEMINI_AVAILABLE
        except ImportError:
            logger.warning("Gemini adapter not available")
            self._availability_checks["gemini"] = False

        try:
            from ..mock import MockAdapter
            self._adapters["mock"] = MockAdapter
            self._availability_checks["mock"] = True
        except ImportError:
            logger.error("Mock adapter not available - this should not happen")
            self._availability_checks["mock"] = False

        self._initialized = True

    def create(
        self,
        provider: str,
        config: Optional[LLMConfig] = None,
        fallback: bool = True,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter.

        Args:
            provider: LLM provider name
            config: Optional configuration
            fallback: Whether to try fallback providers if the requested one fails
            **kwargs: Additional provider-specific arguments

        Returns:
            Configured LLM adapter

        Raises:
            ValueError: If provider is not supported and fallback is False
        """
        self._ensure_initialized()
        provider = provider.lower()

        # Try the requested provider first
        if provider in self._adapters and self._availability_checks.get(provider, False):
            try:
                adapter_class = self._adapters[provider]
                logger.info(f"Creating {provider} adapter")
                return adapter_class(config=config, **kwargs)
            except Exception as e:
                if not fallback:
                    raise
                logger.warning(f"Failed to create {provider} adapter: {e}")

        # If fallback is enabled, try fallback providers
        if fallback:
            return self._create_with_fallback(provider, config, **kwargs)

        # If no fallback and provider not available
        if provider not in self._adapters:
            raise ValueError(f"Unsupported LLM provider: {provider}")
        else:
            raise ValueError(f"LLM provider {provider} is not available")

    def _create_with_fallback(
        self,
        original_provider: str,
        config: Optional[LLMConfig] = None,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter using fallback providers.

        Args:
            original_provider: The originally requested provider
            config: Optional configuration
            **kwargs: Additional provider-specific arguments

        Returns:
            Configured LLM adapter using the first available fallback

        Raises:
            ValueError: If no fallback providers are available
        """
        self._ensure_initialized()
        logger.warning(f"Trying fallback providers for {original_provider}")

        for fallback_provider in self._fallback_order:
            if fallback_provider in self._adapters and self._availability_checks.get(fallback_provider, False):
                try:
                    adapter_class = self._adapters[fallback_provider]
                    logger.info(f"Using fallback provider: {fallback_provider}")
                    return adapter_class(config=config, **kwargs)
                except Exception as e:
                    logger.warning(f"Fallback provider {fallback_provider} failed: {e}")
                    continue

        raise ValueError("No available LLM providers found")

    def register_adapter(
        self,
        name: str,
        adapter_class: Type[LLMAdapter],
        available: bool = True
    ) -> None:
        """
        Register a new LLM adapter.

        Args:
            name: Provider name
            adapter_class: Adapter class
            available: Whether the adapter is available
        """
        self._ensure_initialized()
        self._adapters[name.lower()] = adapter_class
        self._availability_checks[name.lower()] = available
        logger.info(f"Registered adapter: {name}")

    def get_supported_providers(self) -> list[str]:
        """Get list of supported LLM providers."""
        self._ensure_initialized()
        return list(self._adapters.keys())

    def get_available_providers(self) -> list[str]:
        """Get list of available LLM providers."""
        self._ensure_initialized()
        return [name for name, available in self._availability_checks.items() if available]

    def create_from_model(
        self,
        model_name: str,
        config: Optional[LLMConfig] = None,
        fallback: bool = True,
        validate_startup: bool = False,
        **kwargs
    ) -> LLMAdapter:
        """
        Create an LLM adapter by automatically detecting the provider from model name.

        This method implements smart model selection by:
        1. Auto-detecting provider from model name using model registry
        2. Validating model availability if requested
        3. Falling back to available providers if needed

        Args:
            model_name: Name of the model to use
            config: Optional LLM configuration
            fallback: Whether to enable fallback to other providers
            validate_startup: Whether to validate model at startup (fail-fast)
            **kwargs: Additional provider-specific arguments

        Returns:
            Configured LLM adapter

        Raises:
            ValueError: If model is invalid and fallback is False
        """
        logger.debug(f"Creating adapter for model: {model_name}")

        # Validate model if requested (startup validation)
        if validate_startup:
            is_valid, error_msg = self._validate_model(model_name)
            if not is_valid:
                if fallback:
                    logger.warning(f"Model validation failed: {error_msg}, trying fallback")
                    return self._create_with_fallback("unknown", config, **kwargs)
                else:
                    raise ValueError(f"Model validation failed: {error_msg}")
            elif error_msg:  # Warning message
                logger.warning(f"Model warning: {error_msg}")

        # Auto-detect provider from model name
        provider = self._detect_provider_from_model(model_name)

        if provider:
            # Create config with the specific model if not provided
            if config is None:
                config = LLMConfig(model=model_name)
            elif config.model != model_name:
                # Update config with the requested model
                config.model = model_name

            # Try to create adapter with detected provider
            try:
                return self.create(provider=provider, config=config, fallback=fallback, **kwargs)
            except Exception as e:
                if fallback:
                    logger.warning(f"Failed to create {provider} adapter for model {model_name}: {e}")
                    return self._create_with_fallback(provider, config, **kwargs)
                else:
                    raise
        else:
            # Provider not detected, use fallback if enabled
            if fallback:
                logger.warning(f"Could not detect provider for model {model_name}, using fallback")
                if config is None:
                    config = LLMConfig(model=model_name)
                return self._create_with_fallback("unknown", config, **kwargs)
            else:
                raise ValueError(f"Could not detect provider for model: {model_name}")

    def _detect_provider_from_model(self, model_name: str) -> Optional[str]:
        """Detect provider from model name using model registry."""
        try:
            from .model_registry import get_provider_for_model
            provider = get_provider_for_model(model_name)
            if provider:
                logger.debug(f"Detected provider {provider} for model {model_name}")
            return provider
        except ImportError:
            logger.warning("Model registry not available for provider detection")
            return None

    def _validate_model(self, model_name: str) -> Tuple[bool, Optional[str]]:
        """Validate model using model registry."""
        try:
            from .model_registry import validate_model
            return validate_model(model_name)
        except ImportError:
            logger.warning("Model registry not available for validation")
            return True, None  # Assume valid if can't validate


# Global factory instance
_llm_factory = LLMFactory()

# Registry alias for compatibility (lazy access)
def get_adapter_registry():
    """Get the adapter registry (lazy access to avoid initialization issues)."""
    _llm_factory._ensure_initialized()
    return _llm_factory._adapters

# For backward compatibility - use a class to make property work
class _AdapterRegistryProxy:
    @property
    def adapters(self):
        return get_adapter_registry()

ADAPTER_REGISTRY = _AdapterRegistryProxy().adapters


def get_llm_adapter(
    provider: str = "mock",
    config: Optional[LLMConfig] = None,
    fallback: bool = True,
    **kwargs
) -> LLMAdapter:
    """
    Get an LLM adapter based on the provider.

    This is the main factory function for creating LLM adapters with fallback support.

    Args:
        provider: LLM provider name (default: "mock")
        config: Optional LLM configuration
        fallback: Whether to enable fallback to other providers (default: True)
        **kwargs: Additional arguments passed to the adapter

    Returns:
        An initialized LLM adapter ready for use

    Raises:
        ValueError: If provider is not supported and fallback is False
    """
    return _llm_factory.create(
        provider=provider,
        config=config,
        fallback=fallback,
        **kwargs
    )


def get_llm_adapter_by_model(
    model_name: str,
    config: Optional[LLMConfig] = None,
    fallback: bool = True,
    validate_startup: bool = False,
    **kwargs
) -> LLMAdapter:
    """
    Get an LLM adapter by automatically detecting the provider from model name.

    This function enables smart model selection where users can specify just
    the model name and the system automatically determines the correct provider.

    Args:
        model_name: Name of the model to use
        config: Optional LLM configuration (model will be overridden)
        fallback: Whether to enable fallback to other providers (default: True)
        validate_startup: Whether to validate model at startup (default: False)
        **kwargs: Additional arguments passed to the adapter

    Returns:
        An initialized LLM adapter ready for use

    Raises:
        ValueError: If model is invalid and fallback is False

    Examples:
        >>> adapter = get_llm_adapter_by_model("gpt-4o")
        >>> adapter = get_llm_adapter_by_model("claude-3-5-sonnet-20241022")
        >>> adapter = get_llm_adapter_by_model("gemini-2.0-flash")
    """
    return _llm_factory.create_from_model(
        model_name=model_name,
        config=config,
        fallback=fallback,
        validate_startup=validate_startup,
        **kwargs
    )


def get_llm_adapter_smart(
    model_name: Optional[str] = None,
    provider: Optional[str] = None,
    config: Optional[LLMConfig] = None,
    fallback: bool = True,
    component_type: Optional[str] = None,
    **kwargs
) -> LLMAdapter:
    """
    Smart LLM adapter creation that supports both explicit provider and model-based selection.

    This function provides the most flexible interface:
    - If provider is specified, uses explicit provider selection
    - If only model_name is specified, auto-detects provider
    - If neither is specified, uses environment/config defaults
    - Supports component-specific configuration

    Args:
        model_name: Name of the model to use (optional)
        provider: LLM provider name (optional)
        config: Optional LLM configuration
        fallback: Whether to enable fallback to other providers (default: True)
        component_type: Component type for component-specific defaults
                       (e.g., "rag", "agent", "orchestration", "planning")
        **kwargs: Additional arguments passed to the adapter

    Returns:
        An initialized LLM adapter ready for use

    Examples:
        >>> # Explicit provider (backward compatibility)
        >>> adapter = get_llm_adapter_smart(provider="openai")

        >>> # Smart model selection
        >>> adapter = get_llm_adapter_smart(model_name="gpt-4o")

        >>> # Component-specific defaults
        >>> adapter = get_llm_adapter_smart(component_type="rag")

        >>> # Environment defaults
        >>> adapter = get_llm_adapter_smart()
    """
    # Create config with component-specific defaults if not provided
    if config is None:
        config = LLMConfig(model=model_name, component_type=component_type)
    elif model_name and config.model != model_name:
        # Override model in existing config
        config.model = model_name

    # If provider is explicitly specified, use traditional method
    if provider:
        return get_llm_adapter(
            provider=provider,
            config=config,
            fallback=fallback,
            **kwargs
        )

    # If model is specified or available in config, use smart selection
    effective_model = model_name or config.model
    if effective_model:
        return get_llm_adapter_by_model(
            model_name=effective_model,
            config=config,
            fallback=fallback,
            **kwargs
        )

    # Fallback to default provider if no model specified
    logger.warning("No model or provider specified, using default provider")
    return get_llm_adapter(
        provider="mock",
        config=config,
        fallback=fallback,
        **kwargs
    )


def get_llm_adapter_factory() -> LLMFactory:
    """
    Get the global LLM factory instance.

    Returns:
        The global LLMFactory instance
    """
    return _llm_factory


def register_adapter(
    name: str,
    adapter_class: Type[LLMAdapter],
    available: bool = True
) -> None:
    """
    Register a new LLM adapter with the global factory.

    Args:
        name: Provider name
        adapter_class: Adapter class
        available: Whether the adapter is available
    """
    _llm_factory.register_adapter(name, adapter_class, available)
