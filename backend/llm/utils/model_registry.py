"""
Model Registry for LLM Adapters

This module provides a comprehensive registry of LLM models with their
provider mappings, capabilities, and metadata. It enables automatic
provider detection and model validation for dynamic model selection.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ModelCapability(Enum):
    """Enumeration of model capabilities."""
    CHAT = "chat"
    COMPLETION = "completion"
    STREAMING = "streaming"
    FUNCTION_CALLING = "function_calling"
    VISION = "vision"
    CODE = "code"
    REASONING = "reasoning"


@dataclass
class ModelInfo:
    """Information about a specific LLM model with UI-friendly metadata."""
    name: str
    provider: str
    max_tokens: int
    context_window: int
    capabilities: Set[ModelCapability]
    cost_per_1k_input: Optional[float] = None
    cost_per_1k_output: Optional[float] = None
    description: Optional[str] = None
    deprecated: bool = False
    recommended_for: Optional[List[str]] = None

    # UI-friendly metadata for frontend integration
    ui_tooltip: Optional[str] = None
    cost_tier: str = "medium"  # "low", "medium", "high"
    speed_tier: str = "medium"  # "fast", "medium", "slow"
    best_use_cases: Optional[List[str]] = None
    strengths: Optional[List[str]] = None
    limitations: Optional[List[str]] = None


class ModelRegistry:
    """
    Registry for LLM models with provider mappings and capabilities.
    
    This class maintains a comprehensive database of available models,
    their providers, capabilities, and metadata to enable smart model
    selection and automatic provider detection.
    """
    
    def __init__(self):
        """Initialize the model registry."""
        self._models: Dict[str, ModelInfo] = {}
        self._provider_models: Dict[str, List[str]] = {}
        self._populate_registry()
    
    def _populate_registry(self) -> None:
        """Populate the registry with known models."""
        
        # OpenAI Models
        openai_models = [
            ModelInfo(
                name="gpt-4o",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.VISION,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=2.50,
                cost_per_1k_output=10.00,
                description="Most advanced GPT-4 model with vision and reasoning",
                recommended_for=["complex_reasoning", "code_generation", "vision_tasks"],
                ui_tooltip="Premium model with multimodal capabilities. Best for complex reasoning, code generation, and vision tasks. Higher cost but excellent accuracy.",
                cost_tier="high",
                speed_tier="medium",
                best_use_cases=["Complex analysis", "Code generation", "Image understanding", "Strategic planning"],
                strengths=["Multimodal capabilities", "Advanced reasoning", "High accuracy", "Function calling"],
                limitations=["Higher cost", "Moderate speed", "Token limits"]
            ),
            ModelInfo(
                name="gpt-4o-mini",
                provider="openai",
                max_tokens=16384,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE
                },
                cost_per_1k_input=0.15,
                cost_per_1k_output=0.60,
                description="Faster, more affordable GPT-4 model",
                recommended_for=["general_tasks", "rag", "agents"],
                ui_tooltip="Balanced model offering good performance at lower cost. Ideal for most applications including RAG, agents, and general tasks.",
                cost_tier="low",
                speed_tier="fast",
                best_use_cases=["RAG systems", "Chatbots", "Content generation", "General assistance"],
                strengths=["Cost-effective", "Fast responses", "Good general performance", "Function calling"],
                limitations=["No vision capabilities", "Less advanced reasoning than GPT-4o"]
            ),
            ModelInfo(
                name="gpt-4-turbo-preview",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE
                },
                cost_per_1k_input=10.00,
                cost_per_1k_output=30.00,
                description="Previous generation GPT-4 model",
                deprecated=True,
                recommended_for=["legacy_compatibility"],
                ui_tooltip="⚠️ Deprecated model. Consider upgrading to GPT-4o or GPT-4o-mini for better performance and cost.",
                cost_tier="high",
                speed_tier="slow",
                best_use_cases=["Legacy system compatibility"],
                strengths=["Established compatibility"],
                limitations=["Deprecated", "Higher cost", "Slower than newer models"]
            ),
            ModelInfo(
                name="gpt-4.1-2025-04-14",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=10.00,
                cost_per_1k_output=30.00,
                description="Latest GPT-4 model with enhanced reasoning",
                recommended_for=["complex_reasoning", "code_generation"],
                ui_tooltip="Enhanced GPT-4 model with improved reasoning capabilities. Premium option for complex analytical tasks.",
                cost_tier="high",
                speed_tier="medium",
                best_use_cases=["Complex reasoning", "Advanced code generation", "Research analysis"],
                strengths=["Enhanced reasoning", "Advanced capabilities", "High accuracy"],
                limitations=["Premium pricing", "Moderate speed"]
            ),
            ModelInfo(
                name="gpt-3.5-turbo",
                provider="openai",
                max_tokens=4096,
                context_window=16385,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING
                },
                cost_per_1k_input=0.50,
                cost_per_1k_output=1.50,
                description="Fast and affordable model for simple tasks",
                recommended_for=["simple_tasks", "high_volume"],
                ui_tooltip="Budget-friendly option for simple tasks and high-volume applications. Good for basic chatbots and simple content generation.",
                cost_tier="low",
                speed_tier="fast",
                best_use_cases=["Simple chatbots", "Basic content generation", "High-volume processing"],
                strengths=["Very cost-effective", "Fast responses", "Reliable for simple tasks"],
                limitations=["Limited reasoning", "Smaller context window", "Basic capabilities"]
            )
        ]
        
        # Anthropic Models
        anthropic_models = [
            ModelInfo(
                name="claude-3-5-sonnet-20241022",
                provider="anthropic",
                max_tokens=8192,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING,
                    ModelCapability.VISION
                },
                cost_per_1k_input=3.00,
                cost_per_1k_output=15.00,
                description="Most capable Claude model with vision and reasoning",
                recommended_for=["complex_reasoning", "code_generation", "analysis"],
                ui_tooltip="Top-tier Claude model with exceptional reasoning and analysis capabilities. Excellent for complex tasks requiring deep thinking.",
                cost_tier="medium",
                speed_tier="medium",
                best_use_cases=["Complex analysis", "Research", "Code review", "Strategic planning"],
                strengths=["Exceptional reasoning", "Large context window", "Vision capabilities", "Thoughtful responses"],
                limitations=["Higher cost than Haiku", "Moderate speed"]
            ),
            ModelInfo(
                name="claude-3-7-sonnet-20250219",
                provider="anthropic",
                max_tokens=8192,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=3.00,
                cost_per_1k_output=15.00,
                description="Latest Claude Sonnet model with enhanced capabilities",
                recommended_for=["complex_reasoning", "code_generation", "analysis"],
                ui_tooltip="Latest Claude Sonnet with enhanced reasoning capabilities. Ideal for sophisticated analytical and creative tasks.",
                cost_tier="medium",
                speed_tier="medium",
                best_use_cases=["Advanced reasoning", "Code generation", "Content analysis", "Research"],
                strengths=["Latest improvements", "Strong reasoning", "Large context", "Code expertise"],
                limitations=["Premium pricing", "No vision capabilities"]
            ),
            ModelInfo(
                name="claude-3-haiku-20240307",
                provider="anthropic",
                max_tokens=4096,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING
                },
                cost_per_1k_input=0.25,
                cost_per_1k_output=1.25,
                description="Fast and affordable Claude model",
                recommended_for=["simple_tasks", "rag", "high_volume"],
                ui_tooltip="Cost-effective Claude model perfect for RAG systems and high-volume applications. Great balance of quality and affordability.",
                cost_tier="low",
                speed_tier="fast",
                best_use_cases=["RAG systems", "Document processing", "Customer support", "Content summarization"],
                strengths=["Very cost-effective", "Fast responses", "Large context window", "Good quality"],
                limitations=["Simpler reasoning", "No vision or advanced capabilities"]
            )
        ]
        
        # Google Gemini Models
        gemini_models = [
            ModelInfo(
                name="gemini-2.0-flash",
                provider="gemini",
                max_tokens=8192,
                context_window=1000000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.VISION,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=0.075,
                cost_per_1k_output=0.30,
                description="Fast and efficient Gemini model with large context",
                recommended_for=["general_tasks", "rag", "long_context"],
                ui_tooltip="Extremely cost-effective model with massive context window. Perfect for processing large documents and long conversations.",
                cost_tier="low",
                speed_tier="fast",
                best_use_cases=["Large document processing", "Long conversations", "Research analysis", "Cost-sensitive applications"],
                strengths=["Massive context window", "Very cost-effective", "Fast responses", "Multimodal"],
                limitations=["Newer model with less track record", "May have occasional inconsistencies"]
            ),
            ModelInfo(
                name="gemini-2.5-pro-exp-03-25",
                provider="gemini",
                max_tokens=8192,
                context_window=2000000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.VISION,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=3.50,
                cost_per_1k_output=10.50,
                description="Advanced Gemini model for complex tasks",
                recommended_for=["complex_reasoning", "research", "long_context"],
                ui_tooltip="Premium Gemini model with enormous context window. Ideal for complex research and analysis requiring extensive context.",
                cost_tier="medium",
                speed_tier="medium",
                best_use_cases=["Complex research", "Large-scale analysis", "Multi-document reasoning", "Academic work"],
                strengths=["Enormous context window", "Advanced reasoning", "Multimodal capabilities", "Research-oriented"],
                limitations=["Higher cost", "Experimental model", "May have availability constraints"]
            )
        ]
        
        # Register all models
        for model_list in [openai_models, anthropic_models, gemini_models]:
            for model in model_list:
                self.register_model(model)
    
    def register_model(self, model_info: ModelInfo) -> None:
        """Register a model in the registry."""
        self._models[model_info.name] = model_info
        
        # Update provider models mapping
        if model_info.provider not in self._provider_models:
            self._provider_models[model_info.provider] = []
        self._provider_models[model_info.provider].append(model_info.name)
        
        logger.debug(f"Registered model: {model_info.name} -> {model_info.provider}")
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self._models.get(model_name)
    
    def get_provider_for_model(self, model_name: str) -> Optional[str]:
        """Get the provider for a specific model."""
        model_info = self.get_model_info(model_name)
        return model_info.provider if model_info else None
    
    def get_models_by_provider(self, provider: str) -> List[str]:
        """Get all models for a specific provider."""
        return self._provider_models.get(provider, [])
    
    def get_all_models(self) -> Dict[str, ModelInfo]:
        """Get all registered models."""
        return self._models.copy()
    
    def get_available_providers(self) -> List[str]:
        """Get all available providers."""
        return list(self._provider_models.keys())
    
    def get_models_by_capability(self, capability: ModelCapability) -> List[str]:
        """Get models that support a specific capability."""
        return [
            name for name, info in self._models.items()
            if capability in info.capabilities
        ]

    def get_recommended_models(self, task_type: str) -> List[str]:
        """Get models recommended for a specific task type."""
        return [
            name for name, info in self._models.items()
            if info.recommended_for and task_type in info.recommended_for
        ]

    def get_models_by_cost_tier(self, cost_tier: str) -> List[str]:
        """Get models by cost tier (low, medium, high)."""
        return [
            name for name, info in self._models.items()
            if info.cost_tier == cost_tier
        ]

    def get_models_by_speed_tier(self, speed_tier: str) -> List[str]:
        """Get models by speed tier (fast, medium, slow)."""
        return [
            name for name, info in self._models.items()
            if info.speed_tier == speed_tier
        ]

    def get_models_for_frontend(self) -> List[Dict[str, Any]]:
        """Get all models formatted for frontend consumption."""
        models = []
        for name, info in self._models.items():
            if not info.deprecated:  # Exclude deprecated models from frontend
                models.append({
                    "name": info.name,
                    "provider": info.provider,
                    "description": info.description,
                    "ui_tooltip": info.ui_tooltip,
                    "cost_tier": info.cost_tier,
                    "speed_tier": info.speed_tier,
                    "best_use_cases": info.best_use_cases or [],
                    "strengths": info.strengths or [],
                    "limitations": info.limitations or [],
                    "capabilities": [cap.value for cap in info.capabilities],
                    "context_window": info.context_window,
                    "max_tokens": info.max_tokens,
                    "cost_per_1k_input": info.cost_per_1k_input,
                    "cost_per_1k_output": info.cost_per_1k_output
                })

        # Sort by cost tier (low first) then by name
        cost_order = {"low": 0, "medium": 1, "high": 2}
        models.sort(key=lambda x: (cost_order.get(x["cost_tier"], 1), x["name"]))
        return models
    
    def validate_model(self, model_name: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if a model exists and is available.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not model_name:
            return False, "Model name cannot be empty"
        
        model_info = self.get_model_info(model_name)
        if not model_info:
            return False, f"Unknown model: {model_name}"
        
        if model_info.deprecated:
            return True, f"Warning: Model {model_name} is deprecated"
        
        return True, None


# Global registry instance
_model_registry = ModelRegistry()


def get_model_registry() -> ModelRegistry:
    """Get the global model registry instance."""
    return _model_registry


def get_provider_for_model(model_name: str) -> Optional[str]:
    """Get the provider for a specific model."""
    return _model_registry.get_provider_for_model(model_name)


def validate_model(model_name: str) -> Tuple[bool, Optional[str]]:
    """Validate if a model exists and is available."""
    return _model_registry.validate_model(model_name)


def get_recommended_models(task_type: str) -> List[str]:
    """Get models recommended for a specific task type."""
    return _model_registry.get_recommended_models(task_type)


def get_models_by_capability(capability: ModelCapability) -> List[str]:
    """Get models that support a specific capability."""
    return _model_registry.get_models_by_capability(capability)


def get_models_by_cost_tier(cost_tier: str) -> List[str]:
    """Get models by cost tier (low, medium, high)."""
    return _model_registry.get_models_by_cost_tier(cost_tier)


def get_models_by_speed_tier(speed_tier: str) -> List[str]:
    """Get models by speed tier (fast, medium, slow)."""
    return _model_registry.get_models_by_speed_tier(speed_tier)


def get_models_for_frontend() -> List[Dict[str, Any]]:
    """Get all models formatted for frontend consumption."""
    return _model_registry.get_models_for_frontend()
