"""
Model Registry for LLM Adapters

This module provides a comprehensive registry of LLM models with their
provider mappings, capabilities, and metadata. It enables automatic
provider detection and model validation for dynamic model selection.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ModelCapability(Enum):
    """Enumeration of model capabilities."""
    CHAT = "chat"
    COMPLETION = "completion"
    STREAMING = "streaming"
    FUNCTION_CALLING = "function_calling"
    VISION = "vision"
    CODE = "code"
    REASONING = "reasoning"


@dataclass
class ModelInfo:
    """Information about a specific LLM model."""
    name: str
    provider: str
    max_tokens: int
    context_window: int
    capabilities: Set[ModelCapability]
    cost_per_1k_input: Optional[float] = None
    cost_per_1k_output: Optional[float] = None
    description: Optional[str] = None
    deprecated: bool = False
    recommended_for: Optional[List[str]] = None


class ModelRegistry:
    """
    Registry for LLM models with provider mappings and capabilities.
    
    This class maintains a comprehensive database of available models,
    their providers, capabilities, and metadata to enable smart model
    selection and automatic provider detection.
    """
    
    def __init__(self):
        """Initialize the model registry."""
        self._models: Dict[str, ModelInfo] = {}
        self._provider_models: Dict[str, List[str]] = {}
        self._populate_registry()
    
    def _populate_registry(self) -> None:
        """Populate the registry with known models."""
        
        # OpenAI Models
        openai_models = [
            ModelInfo(
                name="gpt-4o",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.VISION,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=2.50,
                cost_per_1k_output=10.00,
                description="Most advanced GPT-4 model with vision and reasoning",
                recommended_for=["complex_reasoning", "code_generation", "vision_tasks"]
            ),
            ModelInfo(
                name="gpt-4o-mini",
                provider="openai",
                max_tokens=16384,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE
                },
                cost_per_1k_input=0.15,
                cost_per_1k_output=0.60,
                description="Faster, more affordable GPT-4 model",
                recommended_for=["general_tasks", "rag", "agents"]
            ),
            ModelInfo(
                name="gpt-4-turbo-preview",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE
                },
                cost_per_1k_input=10.00,
                cost_per_1k_output=30.00,
                description="Previous generation GPT-4 model",
                deprecated=True,
                recommended_for=["legacy_compatibility"]
            ),
            ModelInfo(
                name="gpt-4.1-2025-04-14",
                provider="openai",
                max_tokens=4096,
                context_window=128000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=10.00,
                cost_per_1k_output=30.00,
                description="Latest GPT-4 model with enhanced reasoning",
                recommended_for=["complex_reasoning", "code_generation"]
            ),
            ModelInfo(
                name="gpt-3.5-turbo",
                provider="openai",
                max_tokens=4096,
                context_window=16385,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.FUNCTION_CALLING
                },
                cost_per_1k_input=0.50,
                cost_per_1k_output=1.50,
                description="Fast and affordable model for simple tasks",
                recommended_for=["simple_tasks", "high_volume"]
            )
        ]
        
        # Anthropic Models
        anthropic_models = [
            ModelInfo(
                name="claude-3-5-sonnet-20241022",
                provider="anthropic",
                max_tokens=8192,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING,
                    ModelCapability.VISION
                },
                cost_per_1k_input=3.00,
                cost_per_1k_output=15.00,
                description="Most capable Claude model with vision and reasoning",
                recommended_for=["complex_reasoning", "code_generation", "analysis"]
            ),
            ModelInfo(
                name="claude-3-7-sonnet-20250219",
                provider="anthropic",
                max_tokens=8192,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=3.00,
                cost_per_1k_output=15.00,
                description="Latest Claude Sonnet model with enhanced capabilities",
                recommended_for=["complex_reasoning", "code_generation", "analysis"]
            ),
            ModelInfo(
                name="claude-3-haiku-20240307",
                provider="anthropic",
                max_tokens=4096,
                context_window=200000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING
                },
                cost_per_1k_input=0.25,
                cost_per_1k_output=1.25,
                description="Fast and affordable Claude model",
                recommended_for=["simple_tasks", "rag", "high_volume"]
            )
        ]
        
        # Google Gemini Models
        gemini_models = [
            ModelInfo(
                name="gemini-2.0-flash",
                provider="gemini",
                max_tokens=8192,
                context_window=1000000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.VISION,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=0.075,
                cost_per_1k_output=0.30,
                description="Fast and efficient Gemini model with large context",
                recommended_for=["general_tasks", "rag", "long_context"]
            ),
            ModelInfo(
                name="gemini-2.5-pro-exp-03-25",
                provider="gemini",
                max_tokens=8192,
                context_window=2000000,
                capabilities={
                    ModelCapability.CHAT,
                    ModelCapability.STREAMING,
                    ModelCapability.CODE,
                    ModelCapability.VISION,
                    ModelCapability.REASONING
                },
                cost_per_1k_input=3.50,
                cost_per_1k_output=10.50,
                description="Advanced Gemini model for complex tasks",
                recommended_for=["complex_reasoning", "research", "long_context"]
            )
        ]
        
        # Register all models
        for model_list in [openai_models, anthropic_models, gemini_models]:
            for model in model_list:
                self.register_model(model)
    
    def register_model(self, model_info: ModelInfo) -> None:
        """Register a model in the registry."""
        self._models[model_info.name] = model_info
        
        # Update provider models mapping
        if model_info.provider not in self._provider_models:
            self._provider_models[model_info.provider] = []
        self._provider_models[model_info.provider].append(model_info.name)
        
        logger.debug(f"Registered model: {model_info.name} -> {model_info.provider}")
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self._models.get(model_name)
    
    def get_provider_for_model(self, model_name: str) -> Optional[str]:
        """Get the provider for a specific model."""
        model_info = self.get_model_info(model_name)
        return model_info.provider if model_info else None
    
    def get_models_by_provider(self, provider: str) -> List[str]:
        """Get all models for a specific provider."""
        return self._provider_models.get(provider, [])
    
    def get_all_models(self) -> Dict[str, ModelInfo]:
        """Get all registered models."""
        return self._models.copy()
    
    def get_available_providers(self) -> List[str]:
        """Get all available providers."""
        return list(self._provider_models.keys())
    
    def get_models_by_capability(self, capability: ModelCapability) -> List[str]:
        """Get models that support a specific capability."""
        return [
            name for name, info in self._models.items()
            if capability in info.capabilities
        ]
    
    def get_recommended_models(self, task_type: str) -> List[str]:
        """Get models recommended for a specific task type."""
        return [
            name for name, info in self._models.items()
            if info.recommended_for and task_type in info.recommended_for
        ]
    
    def validate_model(self, model_name: str) -> Tuple[bool, Optional[str]]:
        """
        Validate if a model exists and is available.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not model_name:
            return False, "Model name cannot be empty"
        
        model_info = self.get_model_info(model_name)
        if not model_info:
            return False, f"Unknown model: {model_name}"
        
        if model_info.deprecated:
            return True, f"Warning: Model {model_name} is deprecated"
        
        return True, None


# Global registry instance
_model_registry = ModelRegistry()


def get_model_registry() -> ModelRegistry:
    """Get the global model registry instance."""
    return _model_registry


def get_provider_for_model(model_name: str) -> Optional[str]:
    """Get the provider for a specific model."""
    return _model_registry.get_provider_for_model(model_name)


def validate_model(model_name: str) -> Tuple[bool, Optional[str]]:
    """Validate if a model exists and is available."""
    return _model_registry.validate_model(model_name)


def get_recommended_models(task_type: str) -> List[str]:
    """Get models recommended for a specific task type."""
    return _model_registry.get_recommended_models(task_type)


def get_models_by_capability(capability: ModelCapability) -> List[str]:
    """Get models that support a specific capability."""
    return _model_registry.get_models_by_capability(capability)
