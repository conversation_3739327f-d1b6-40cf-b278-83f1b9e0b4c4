#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to visualize the orchestration flow.
Generates a graph visualization of the task execution flow and agent conversations.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import graphviz
from ..orchestration.state import LangGraphState, TaskStatus

logger = logging.getLogger(__name__)

async def generate_visualization(state: LangGraphState) -> None:
    """
    Generate a visualization of the orchestration flow.
    
    Args:
        state: The final state of the orchestration
    """
    # Create a new directed graph
    dot = graphviz.Digraph(
        comment='Orchestration Flow',
        format='png',
        engine='dot'
    )
    
    # Set graph attributes
    dot.attr(rankdir='TB')
    dot.attr('node', shape='box', style='rounded,filled', fillcolor='lightblue')
    
    # Create subgraphs for different visualization aspects
    with dot.subgraph(name='cluster_0') as c:
        c.attr(label='Orchestration Steps')
        # Add nodes for each step
        steps = ["planning", "task_assignment", "task_execution", "review", "done"]
        for step in steps:
            c.node(step, step.capitalize())
        
        # Add edges between steps
        for i in range(len(steps) - 1):
            c.edge(steps[i], steps[i + 1])
    
    # Add task execution timeline
    with dot.subgraph(name='cluster_1') as c:
        c.attr(label='Task Execution Timeline')
        if state.tasks:
            for task in state.tasks:
                task_id = f"task_{task.task_id}"
                task_label = f"""
                Task: {task.description[:50]}...
                Status: {task.status}
                Agent: {task.assigned_agent or 'Unassigned'}
                """
                
                c.node(
                    task_id,
                    task_label,
                    shape='note',
                    fillcolor='lightyellow'
                )
                
                # Add edges for task dependencies
                for dep in task.dependencies:
                    c.edge(f"task_{dep}", task_id)
    
    # Add agent interaction timeline
    with dot.subgraph(name='cluster_2') as c:
        c.attr(label='Agent Interaction Timeline')
        if state.context.get("tracer_data", {}).get("conversations"):
            conversations = state.context["tracer_data"]["conversations"]
            for i, conv in enumerate(conversations):
                interaction_id = f"interaction_{i}"
                interaction_label = f"""
                Time: {conv.get('timestamp', 'Unknown')}
                From: {conv.get('from_agent', 'Unknown')}
                To: {conv.get('to_agent', 'Unknown')}
                Message: {conv.get('message', '')[:100]}...
                """
                
                c.node(
                    interaction_id,
                    interaction_label,
                    shape='note',
                    fillcolor='lightgreen'
                )
                
                # Add edges to show conversation flow
                if i > 0:
                    c.edge(f"interaction_{i-1}", interaction_id)
    
    # Add agent state changes
    with dot.subgraph(name='cluster_3') as c:
        c.attr(label='Agent State Changes')
        if state.context.get("tracer_data", {}).get("agent_states"):
            agent_states = state.context["tracer_data"]["agent_states"]
            for agent, states in agent_states.items():
                for i, state_update in enumerate(states):
                    state_id = f"state_{agent}_{i}"
                    state_label = f"""
                    Agent: {agent}
                    Time: {state_update.get('timestamp', 'Unknown')}
                    State: {state_update.get('state', {})}
                    """
                    
                    c.node(
                        state_id,
                        state_label,
                        shape='note',
                        fillcolor='lightpink'
                    )
                    
                    # Add edges to show state progression
                    if i > 0:
                        c.edge(f"state_{agent}_{i-1}", state_id)
    
    # Add task progress tracking
    with dot.subgraph(name='cluster_4') as c:
        c.attr(label='Task Progress Tracking')
        if state.context.get("tracer_data", {}).get("task_progress"):
            task_progress = state.context["tracer_data"]["task_progress"]
            for task_id, updates in task_progress.items():
                for i, update in enumerate(updates):
                    progress_id = f"progress_{task_id}_{i}"
                    progress_label = f"""
                    Task: {task_id}
                    Time: {update.get('timestamp', 'Unknown')}
                    Status: {update.get('status', 'Unknown')}
                    Agent: {update.get('agent', 'Unknown')}
                    Details: {update.get('details', {})}
                    """
                    
                    c.node(
                        progress_id,
                        progress_label,
                        shape='note',
                        fillcolor='lightcyan'
                    )
                    
                    # Add edges to show progress
                    if i > 0:
                        c.edge(f"progress_{task_id}_{i-1}", progress_id)
    
    # Add summary information
    if "final_summary" in state.context:
        summary = state.context["final_summary"]
        dot.attr(label=f"""
        Orchestration Summary
        Total Tasks: {summary['total_tasks']}
        Completed: {summary['completed_tasks']}
        Failed: {summary['failed_tasks']}
        Success Rate: {summary['success_rate']:.2%}
        Total Time: {summary['total_time']:.2f}s
        Agent Interactions: {len(state.context.get('tracer_data', {}).get('conversations', []))}
        """)
    
    # Save the visualization
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = Path("visualizations") / f"orchestration_{timestamp}"
    output_path.parent.mkdir(exist_ok=True)
    
    dot.render(str(output_path), view=True)
    logger.info(f"Visualization saved to {output_path}.png")

def main():
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # This script is typically called from test_orchestration.py
    logger.info("This script should be called from test_orchestration.py")

if __name__ == "__main__":
    main() 