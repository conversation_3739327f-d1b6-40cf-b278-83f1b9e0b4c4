#!/usr/bin/env python
"""
Environment Variable Check Script

This script verifies that environment variables are being loaded correctly
from the .env file and are accessible to the application.

Usage:
    python check_env.py
"""

import os
import sys
from pathlib import Path

# Add backend to path for imports
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))

from app.config import get_settings

def main():
    """Check environment variables."""
    print("🔍 Checking environment variables...")
    
    # Get settings
    settings = get_settings()
    
    # Check OpenAI API key
    openai_key = settings.OPENAI_API_KEY
    if openai_key:
        print("✅ OpenAI API key is set")
        print(f"   Key starts with: {openai_key[:8]}...")
    else:
        print("❌ OpenAI API key is not set")
    
    # Check database URL
    db_url = settings.DATABASE_URL
    if db_url:
        print("✅ Database URL is set")
        print(f"   URL: {db_url}")
    else:
        print("❌ Database URL is not set")
    
    # Check other important settings
    print("\n📊 Other Settings:")
    print(f"   DEBUG: {settings.DEBUG}")
    print(f"   DEVELOPMENT_MODE: {settings.DEVELOPMENT_MODE}")
    print(f"   LOG_LEVEL: {settings.LOG_LEVEL}")
    print(f"   DEFAULT_EMBEDDING_MODEL: {settings.DEFAULT_EMBEDDING_MODEL}")
    print(f"   VECTOR_STORE_TYPE: {settings.VECTOR_STORE_TYPE}")

if __name__ == "__main__":
    main() 