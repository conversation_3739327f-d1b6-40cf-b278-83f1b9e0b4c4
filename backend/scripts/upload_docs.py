#!/usr/bin/env python3
"""
Script to upload mock documents to the database for testing.
Supports various document formats and handles vector embeddings.
"""

import argparse
import asyncio
import logging
from pathlib import Path
from typing import List, Optional
import gc
import os
import psutil
import uuid
from sqlalchemy import text
import json

from backend.app.core.db.database import get_async_db_session
from backend.rag.embeddings import get_embedding_model

logger = logging.getLogger(__name__)

async def save_document(conn, content, metadata, embedding, document_id):
    chunk_id = str(uuid.uuid4())
    await conn.execute(
        text("""
            INSERT INTO document_chunks (id, document_id, content, chunk_metadata, embedding)
            VALUES (:id, :document_id, :content, :chunk_metadata, :embedding)
        """),
        {
            "id": chunk_id,
            "document_id": document_id,
            "content": content,
            "chunk_metadata": json.dumps(metadata),
            "embedding": str(list(embedding))
        }
    )
    await conn.commit()

async def save_parent_document(conn, title, source, metadata):
    document_id = str(uuid.uuid4())
    await conn.execute(
        text("""
            INSERT INTO documents (id, title, source, doc_metadata)
            VALUES (:id, :title, :source, :doc_metadata)
        """),
        {
            "id": document_id,
            "title": title,
            "source": source,
            "doc_metadata": json.dumps(metadata)
        }
    )
    await conn.commit()
    return document_id

async def upload_documents(
    docs_dir: Path,
    file_patterns: List[str] = ["*.txt", "*.md", "*.pdf"],
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    batch_size: int = 5  # Process 5 chunks at a time
) -> None:
    """
    Upload documents from a directory to the database.
    
    Args:
        docs_dir: Directory containing documents to upload
        file_patterns: List of file patterns to match
        chunk_size: Size of text chunks for embedding
        chunk_overlap: Overlap between chunks
        batch_size: Number of chunks to process at once
    """
    # Initialize embedding model (uses config or env vars)
    embedding_model = get_embedding_model()
    
    # Get database connection
    async with get_async_db_session() as conn:
        # Find all matching files
        files = []
        for pattern in file_patterns:
            files.extend(docs_dir.glob(pattern))
        
        if not files:
            logger.warning(f"No files found matching patterns {file_patterns} in {docs_dir}")
            return
        
        logger.info(f"Found {len(files)} files to process")
        
        # Process each file
        for file_idx, file_path in enumerate(files):
            try:
                logger.info(f"Processing {file_path}")
                content = file_path.read_text()
                # Save parent document and get its ID
                parent_metadata = {"source": str(file_path)}
                document_id = await save_parent_document(conn, title=file_path.name, source=str(file_path), metadata=parent_metadata)
                chunks = chunk_text(content, max_chars=500)
                logger.info(f"Split document into {len(chunks)} chunks (max_chars=500)")
                process = psutil.Process(os.getpid())
                for chunk_idx, chunk in enumerate(chunks):
                    logger.info(f"[DEBUG] Embedding chunk {chunk_idx+1}/{len(chunks)}: length={len(chunk)}, preview={repr(chunk[:100])}")
                    try:
                        embeddings = await embedding_model.embed_documents([chunk])
                            await save_document(
                                conn,
                                content=chunk,
                                metadata={
                                    "source": str(file_path),
                                "chunk_index": chunk_idx,
                                    "total_chunks": len(chunks)
                                },
                            embedding=embeddings[0],
                            document_id=document_id
                            )
                        mem_mb = process.memory_info().rss / 1024 / 1024
                        logger.info(f"[DEBUG] Memory usage after embedding: {mem_mb:.2f} MB")
                        del embeddings
                        gc.collect()
                    except Exception as e:
                        logger.error(f"Error embedding chunk {chunk_idx+1}: {str(e)}")
                        continue
                logger.info(f"Successfully processed {file_path}")
            except Exception as e:
                logger.error(f"Error processing {file_path}: {str(e)}")
                continue

def chunk_text(text: str, max_chars=500) -> list[str]:
    return [text[i:i+max_chars] for i in range(0, len(text), max_chars)]

def main():
    parser = argparse.ArgumentParser(description="Upload documents to the database")
    parser.add_argument(
        "--docs-dir",
        type=Path,
        default=Path("mock_docs"),
        help="Directory containing documents to upload"
    )
    parser.add_argument(
        "--patterns",
        nargs="+",
        default=["*.txt", "*.md", "*.pdf"],
        help="File patterns to match"
    )
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=1000,
        help="Size of text chunks for embedding"
    )
    parser.add_argument(
        "--chunk-overlap",
        type=int,
        default=200,
        help="Overlap between chunks"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=5,
        help="Number of chunks to process at once"
    )
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the upload
    asyncio.run(upload_documents(
        args.docs_dir,
        args.patterns,
        args.chunk_size,
        args.chunk_overlap,
        args.batch_size
    ))

if __name__ == "__main__":
    main() 