#!/usr/bin/env python3
"""
Comprehensive LLM Integration Test

This script tests the complete LLM configuration system integration including:
- Model registry functionality
- Environment-driven configuration
- Smart model selection
- Provider auto-detection
- Configuration cascade
- Backward compatibility
- End-to-end functionality
"""

import os
import sys
import logging
import asyncio
from typing import Optional

# Add backend to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging to suppress warnings during testing
logging.basicConfig(
    level=logging.ERROR,  # Only show errors
    format='%(levelname)s: %(message)s'
)

def test_core_llm_system():
    """Test the core LLM system functionality."""
    print("🧪 COMPREHENSIVE LLM INTEGRATION TEST")
    print("=" * 60)
    
    # Import core components
    try:
        from llm.utils.model_registry import (
            get_model_registry,
            get_provider_for_model,
            validate_model,
            get_recommended_models,
            ModelCapability,
            get_models_by_capability
        )
        from llm.utils.config import LLMConfig
        from llm.utils.factory import get_llm_adapter
        print("✅ Core LLM imports successful")
    except Exception as e:
        print(f"❌ Core LLM imports failed: {e}")
        return False
    
    # Test 1: Model Registry Comprehensive Test
    print("\n📋 Testing Model Registry...")
    registry = get_model_registry()
    
    # Test provider detection for various models
    test_models = [
        ("gpt-4o", "openai"),
        ("gpt-4o-mini", "openai"),
        ("claude-3-5-sonnet-20241022", "anthropic"),
        ("claude-3-haiku-20240307", "anthropic"),
        ("gemini-2.0-flash", "gemini"),
        ("gemini-2.5-pro-exp-03-25", "gemini"),
    ]
    
    for model, expected_provider in test_models:
        detected = get_provider_for_model(model)
        status = "✅" if detected == expected_provider else "❌"
        print(f"   {status} {model} → {detected}")
    
    # Test model validation
    print("\n🔍 Testing Model Validation...")
    validation_tests = [
        ("gpt-4o", True, None),
        ("claude-3-5-sonnet-20241022", True, None),
        ("gpt-4-turbo-preview", True, "deprecated"),
        ("invalid-model-name", False, "unknown"),
    ]
    
    for model, should_be_valid, expected_warning in validation_tests:
        is_valid, message = validate_model(model)
        status = "✅" if is_valid == should_be_valid else "❌"
        warning_check = "✅" if not expected_warning or (message and expected_warning in message.lower()) else "❌"
        print(f"   {status}{warning_check} {model}: valid={is_valid}, msg={message}")
    
    # Test capability queries
    print("\n🎯 Testing Capability Queries...")
    streaming_models = get_models_by_capability(ModelCapability.STREAMING)
    vision_models = get_models_by_capability(ModelCapability.VISION)
    code_models = get_models_by_capability(ModelCapability.CODE)
    
    print(f"   ✅ Streaming models: {len(streaming_models)} found")
    print(f"   ✅ Vision models: {len(vision_models)} found")
    print(f"   ✅ Code models: {len(code_models)} found")
    
    # Test recommendations
    print("\n💡 Testing Recommendations...")
    rag_recommendations = get_recommended_models("rag")
    code_recommendations = get_recommended_models("code_generation")
    
    print(f"   ✅ RAG recommendations: {rag_recommendations[:3]}")
    print(f"   ✅ Code recommendations: {code_recommendations[:3]}")
    
    return True

def test_configuration_system():
    """Test the configuration system."""
    print("\n⚙️  Testing Configuration System...")
    
    from llm.utils.config import LLMConfig
    
    # Test default configuration
    print("\n1. Default Configuration:")
    config = LLMConfig()
    print(f"   ✅ Default model: {config.model}")
    print(f"   ✅ Default temperature: {config.temperature}")
    print(f"   ✅ Default timeout: {config.timeout}")
    
    # Test component-specific configuration
    print("\n2. Component-Specific Configuration:")
    components = ["rag", "agent", "orchestration", "planning"]
    for component in components:
        config = LLMConfig(component_type=component)
        print(f"   ✅ {component.upper()}: {config.model}")
    
    # Test explicit overrides
    print("\n3. Explicit Overrides:")
    config = LLMConfig(
        model="claude-3-5-sonnet-20241022",
        temperature=0.1,
        max_tokens=2000
    )
    print(f"   ✅ Override model: {config.model}")
    print(f"   ✅ Override temperature: {config.temperature}")
    print(f"   ✅ Override max_tokens: {config.max_tokens}")
    
    # Test provider detection
    print("\n4. Provider Detection:")
    provider = config.get_provider()
    print(f"   ✅ Detected provider: {provider}")
    
    # Test validation
    print("\n5. Configuration Validation:")
    is_valid, message = config.validate()
    print(f"   ✅ Config valid: {is_valid}, message: {message}")
    
    return True

def test_factory_system():
    """Test the factory system."""
    print("\n🏭 Testing Factory System...")
    
    from llm.utils.factory import get_llm_adapter
    
    # Test traditional provider-based creation
    print("\n1. Traditional Provider-Based Creation:")
    try:
        adapter = get_llm_adapter(provider="mock")
        print(f"   ✅ Mock adapter: {type(adapter).__name__}")
    except Exception as e:
        print(f"   ❌ Mock adapter failed: {e}")
    
    # Test model-based creation
    print("\n2. Model-Based Creation:")
    test_models = ["gpt-4o-mini", "claude-3-haiku-20240307", "gemini-2.0-flash"]

    for model in test_models:
        try:
            adapter = get_llm_adapter(model=model, fallback=True)
            print(f"   ✅ {model}: {type(adapter).__name__}")
        except Exception as e:
            print(f"   ❌ {model}: {e}")

    # Test unified factory scenarios
    print("\n3. Unified Factory Scenarios:")

    scenarios = [
        ("Model only", {"model": "gpt-4o-mini"}),
        ("Provider only", {"provider": "mock"}),
        ("Component type", {"component_type": "rag"}),
        ("Environment defaults", {}),
        ("Model with overrides", {"model": "gpt-4o", "temperature": 0.1, "max_tokens": 2000}),
    ]

    for name, kwargs in scenarios:
        try:
            adapter = get_llm_adapter(**kwargs, fallback=True)
            print(f"   ✅ {name}: {type(adapter).__name__}")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    return True

def test_environment_integration():
    """Test environment variable integration."""
    print("\n🌍 Testing Environment Integration...")
    
    # Show current environment variables
    print("\n1. Environment Variables:")
    env_vars = [
        "LLM_DEFAULT_MODEL",
        "RAG_LLM_MODEL",
        "AGENT_LLM_MODEL",
        "ORCHESTRATION_LLM_MODEL",
        "PLANNING_LLM_MODEL"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "Not set")
        print(f"   {var}: {value}")
    
    # Test configuration cascade
    print("\n2. Configuration Cascade:")
    from llm.utils.config import LLMConfig
    
    # Test system default
    config = LLMConfig()
    print(f"   ✅ System default: {config.model}")
    
    # Test component-specific
    rag_config = LLMConfig(component_type="rag")
    print(f"   ✅ RAG component: {rag_config.model}")
    
    return True

async def test_async_functionality():
    """Test async functionality."""
    print("\n🔄 Testing Async Functionality...")
    
    from llm.utils.factory import get_llm_adapter

    try:
        # Create a mock adapter
        adapter = get_llm_adapter(provider="mock", fallback=True)
        
        # Test async chat
        messages = [{"role": "user", "content": "Hello, world!"}]
        response = await adapter.chat(messages)
        print(f"   ✅ Async chat response: {response[:50]}...")
        
        # Test token counting
        prompt_tokens, completion_tokens = await adapter.get_token_count(messages)
        print(f"   ✅ Token count: {prompt_tokens} prompt, {completion_tokens} completion")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Async functionality failed: {e}")
        return False

def main():
    """Run comprehensive integration tests."""
    print("🚀 Starting Comprehensive LLM Integration Tests")
    print("=" * 60)
    
    success = True
    
    # Run synchronous tests
    success &= test_core_llm_system()
    success &= test_configuration_system()
    success &= test_factory_system()
    success &= test_environment_integration()
    
    # Run async tests
    try:
        success &= asyncio.run(test_async_functionality())
    except Exception as e:
        print(f"❌ Async tests failed: {e}")
        success = False
    
    # Final results
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ Model registry working correctly")
        print("✅ Configuration system working correctly")
        print("✅ Factory system working correctly")
        print("✅ Environment integration working correctly")
        print("✅ Async functionality working correctly")
        print("✅ System is ready for production use")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please review the output above for details")
        sys.exit(1)

if __name__ == "__main__":
    main()
