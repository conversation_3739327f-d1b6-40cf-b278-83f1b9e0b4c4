#!/usr/bin/env python3
"""
Test script for agents implementation.

This script validates that the agents are properly implemented and can be instantiated
without errors. It's a basic smoke test to ensure the implementation is sound.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_agents_implementation():
    """Test the agents implementation."""
    print("🧪 Testing Agents Implementation")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from backend.agents import BaseAgent, CoCEOAgent, MarketingAgent, FinanceAgent
        from backend.llm import get_llm_adapter
        from backend.orchestration.memory import GlobalMemory
        from backend.orchestration.state import Task, TaskType, LangGraphState, OrchestrationStep
        from backend.orchestration.utils.shared_services import ContextService, TaskValidator, ServiceInitializer
        print("✅ All imports successful")
        
        # Test LLM adapter creation
        print("\n🤖 Testing LLM adapter creation...")
        llm_adapter = get_llm_adapter("mock", fallback=True)
        print(f"✅ LLM adapter created: {type(llm_adapter).__name__}")
        
        # Test memory creation
        print("\n🧠 Testing memory creation...")
        memory = GlobalMemory()
        print("✅ Memory created successfully")
        
        # Test shared services initialization
        print("\n🔄 Testing shared services initialization...")
        context_service = ContextService(llm_adapter)
        task_validator = TaskValidator()
        service_initializer = ServiceInitializer()
        print("✅ Shared services initialized successfully")
        
        # Test CoCEO agent creation
        print("\n👑 Testing CoCEO agent creation...")
        co_ceo = CoCEOAgent(
            llm_adapter=llm_adapter,
            memory=memory
        )
        print("✅ CoCEO agent created successfully")
        print(f"   - Agent name: {co_ceo.agent_name}")
        print(f"   - Capabilities: {co_ceo.capabilities}")
        print(f"   - Available agents: {list(co_ceo.agent_capabilities.keys())}")
        
        # Test Marketing agent creation
        print("\n📢 Testing Marketing agent creation...")
        marketing = MarketingAgent(
            llm_adapter=llm_adapter,
            memory=memory
        )
        print("✅ Marketing agent created successfully")
        print(f"   - Agent name: {marketing.agent_name}")
        print(f"   - Capabilities: {marketing.capabilities}")
        print(f"   - Domain keywords: {marketing.domain_keywords}")
        
        # Test Finance agent creation
        print("\n💰 Testing Finance agent creation...")
        finance = FinanceAgent(
            llm_adapter=llm_adapter,
            memory=memory
        )
        print("✅ Finance agent created successfully")
        print(f"   - Agent name: {finance.agent_name}")
        print(f"   - Capabilities: {finance.capabilities}")
        print(f"   - Domain keywords: {finance.domain_keywords}")
        
        # Test task creation
        print("\n📋 Testing task creation...")
        test_task = Task(
            description="Test task for agent validation",
            task_type=TaskType.REASONING,
            assigned_agent="CoCEO"
        )
        print(f"✅ Task created: {test_task.task_id}")
        print(f"   - Description: {test_task.description}")
        print(f"   - Type: {test_task.task_type}")
        print(f"   - Metadata: {test_task.metadata}")
        
        # Test task validation
        print("\n✅ Testing task validation...")
        is_valid, error_msg = task_validator.validate_task(test_task)
        print(f"   - Task validation result: {is_valid}")
        if not is_valid:
            print(f"   - Validation error: {error_msg}")
        
        # Test state creation
        print("\n🔄 Testing state creation...")
        test_state = LangGraphState(
            user_input="Test user input for validation",
            tasks=[test_task],
            current_step=OrchestrationStep.PLANNING
        )
        print("✅ LangGraphState created successfully")
        print(f"   - User input: {test_state.user_input[:50]}...")
        print(f"   - Current step: {test_state.current_step}")
        print(f"   - Tasks count: {len(test_state.tasks)}")
        
        # Test CoCEO methods (without actual LLM calls)
        print("\n🎯 Testing CoCEO methods...")
        
        # Test strategic context gathering (will use mock data)
        print("   - Testing strategic context gathering...")
        strategic_context = await co_ceo.gather_strategic_context(
            "test query",
            {"search_filters": {"test": "value"}}
        )
        print(f"     ✅ Strategic context keys: {list(strategic_context.keys())}")
        
        # Test plan_execution method
        print("   - Testing plan_execution method...")
        tasks, analysis = await co_ceo.plan_execution(
            user_input="Test user input for planning",
            context={"test": "context"}
        )
        print(f"     ✅ Plan execution returned {len(tasks)} tasks and analysis: {analysis[:60]}...")
        
        # Test Marketing agent metrics
        print("\n📊 Testing Marketing agent metrics...")
        marketing_response = """
        Campaign Performance Analysis:
        - Brand Awareness: 75%
        - Engagement Rate: 45%
        - Conversion Rate: 12%
        - Customer Retention: 85%
        """
        marketing_metrics = marketing._extract_marketing_metrics(marketing_response)
        print(f"   - Extracted metrics: {marketing_metrics}")
        
        # Test Finance agent metrics
        print("\n📈 Testing Finance agent metrics...")
        finance_response = """
        Financial Performance Analysis:
        - Gross Margin: 45%
        - Current Ratio: 2.5
        - Asset Turnover: 1.8
        - Revenue Growth: 15%
        - Debt Ratio: 0.4
        """
        finance_metrics = finance._extract_financial_metrics(finance_response)
        print(f"   - Extracted metrics: {finance_metrics}")
        
        # Test memory integration
        print("\n🧠 Testing memory integration...")
        memory_count_before = len(memory.memory_units)
        
        # Add some test memory
        from backend.orchestration.memory import MemoryUnits
        test_memory = MemoryUnits(
            memory_id="test_id",
            timestamp=None,
            current_step=OrchestrationStep.PLANNING,
            content="Test memory content",
            agent="CoCEO",
            task_id=test_task.task_id,
            tags=["test", "validation"],
            metadata={
                "timestamp": "2024-01-01T00:00:00",
                "metrics": {
                    "marketing": marketing_metrics,
                    "finance": finance_metrics
                }
            }
        )
        memory.memory_units.append(test_memory)
        
        memory_count_after = len(memory.memory_units)
        print(f"✅ Memory added: {memory_count_after - memory_count_before} new entries")
        
        # Test memory retrieval with metrics
        retrieved_memories = [
            m for m in memory.memory_units
            if m.agent == "CoCEO" and any(tag in m.tags for tag in ["test"])
        ]
        print(f"✅ Memory retrieval: {len(retrieved_memories)} memories found")
        if retrieved_memories:
            print(f"   - Memory metadata: {retrieved_memories[0].metadata}")
        
        # Test context service
        print("\n🔍 Testing context service...")
        context_result = await context_service.gather_context(
            "test query",
            {"user_input": "test input"}
        )
        print(f"✅ Context service result keys: {list(context_result.keys())}")
        
        print("\n🎉 All tests passed successfully!")
        print("✅ Agents implementation is working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    try:
        success = asyncio.run(test_agents_implementation())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
