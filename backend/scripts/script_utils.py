#!/usr/bin/env python3
"""
Script Utilities

This module provides common utilities for all backend scripts to eliminate
code duplication and standardize script patterns.
"""

import sys
import logging
from pathlib import Path
from typing import Optional


def setup_script_environment() -> Path:
    """
    Set up the script environment by adding backend to Python path.

    This function ensures that all scripts can import backend modules
    regardless of where they're run from.

    Returns:
        Path to the backend directory
    """
    backend_path = Path(__file__).parent.parent
    if str(backend_path) not in sys.path:
        sys.path.insert(0, str(backend_path))
    return backend_path


def setup_script_logging(script_name: str, log_level: str = "INFO") -> logging.Logger:
    """
    Set up logging for a script.

    Args:
        script_name: Name of the script
        log_level: Logging level to use

    Returns:
        Logger instance for the script
    """
    # Set up logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    return logging.getLogger(script_name)


def validate_script_prerequisites(
    require_database: bool = True,
    require_config: bool = True,
    logger: Optional[logging.Logger] = None
) -> bool:
    """
    Validate script prerequisites.

    Args:
        require_database: Whether database is required
        require_config: Whether configuration is required
        logger: Logger instance to use

    Returns:
        True if all prerequisites are met, False otherwise
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    try:
        # Validate database if required
        if require_database:
            from backend.app.core.db.database import check_database_connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False

        # Validate configuration if required
        if require_config:
            from backend.app.config import get_settings
            settings = get_settings()
            if not settings:
                logger.error("Configuration validation failed")
                return False

        return True

    except Exception as e:
        logger.error(f"Error validating prerequisites: {e}")
        return False


class ScriptContext:
    """
    Context manager for script setup and cleanup.

    This provides a convenient way to set up the environment,
    logging, and prerequisites for a script.
    """

    def __init__(
        self,
        script_name: str,
        log_level: str = "INFO",
        require_database: bool = True,
        require_config: bool = True
    ):
        """
        Initialize script context.

        Args:
            script_name: Name of the script
            log_level: Logging level to use
            require_database: Whether database is required
            require_config: Whether configuration is required
        """
        self.script_name = script_name
        self.log_level = log_level
        self.require_database = require_database
        self.require_config = require_config
        self.logger = None
        self.backend_path = None

    def __enter__(self):
        """Enter script context."""
        # Set up environment
        self.backend_path = setup_script_environment()

        # Set up logging
        self.logger = setup_script_logging(self.script_name, self.log_level)

        # Validate prerequisites
        if not validate_script_prerequisites(
            require_database=self.require_database,
            require_config=self.require_config,
            logger=self.logger
        ):
            raise RuntimeError("Script prerequisites not met")

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit script context."""
        # Clean up any resources if needed
        pass


def get_database_session():
    """
    Get a database session for scripts.

    Returns:
        Database session context manager
    """
    from backend.app.core.db.database import get_db_context
    return get_db_context()


def ensure_pgvector_extension():
    """
    Ensure pgvector extension is available.

    Returns:
        True if successful, False otherwise
    """
    try:
        from backend.app.core.db.database import ensure_pgvector_extension as _ensure_pgvector
        _ensure_pgvector()
        return True
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to ensure pgvector extension: {e}")
        return False
