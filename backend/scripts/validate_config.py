#!/usr/bin/env python
"""
Configuration Validation Script

This script validates that the configuration system is working correctly
and all required environment variables are properly set.

Usage:
    python validate_config.py
"""

import sys

# Import common script utilities
from script_utils import (
    setup_script_environment,
    setup_script_logging,
    ScriptContext,
    script_main
)

# Set up environment
setup_script_environment()


def validate_configuration(logger=None):
    """Validate the configuration system."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("🔧 Validating configuration system...")

    try:
        # Test configuration import
        from app.config import settings, get_settings
        logger.info("✅ Configuration module imported successfully")

        # Test settings loading
        config = get_settings()
        logger.info("✅ Settings loaded successfully")

        # Display key configuration values
        logger.info(f"📊 Configuration Summary:")
        logger.info(f"  - DEBUG: {config.DEBUG}")
        logger.info(f"  - DEVELOPMENT_MODE: {config.DEVELOPMENT_MODE}")
        logger.info(f"  - LOG_LEVEL: {config.LOG_LEVEL}")
        logger.info(f"  - DATABASE_URL: {config.DATABASE_URL[:50]}...")
        logger.info(f"  - DEFAULT_EMBEDDING_MODEL: {config.DEFAULT_EMBEDDING_MODEL}")
        logger.info(f"  - VECTOR_STORE_TYPE: {config.VECTOR_STORE_TYPE}")
        logger.info(f"  - VECTOR_DIMENSION: {config.VECTOR_DIMENSION}")

        # Check API keys
        api_keys = {
            "OpenAI": config.OPENAI_API_KEY,
            "Anthropic": config.ANTHROPIC_API_KEY,
            "Google": config.GOOGLE_API_KEY,
        }

        available_keys = [name for name, key in api_keys.items() if key]
        if available_keys:
            logger.info(f"🔑 Available API keys: {', '.join(available_keys)}")
        else:
            logger.warning("⚠️  No API keys configured")

        # Validate configuration
        validation_errors = config.validate_api_keys()
        if validation_errors:
            logger.warning("⚠️  Configuration warnings:")
            for error in validation_errors:
                logger.warning(f"    - {error}")
        else:
            logger.info("✅ Configuration validation passed")

        return True

    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False


def validate_database(logger=None):
    """Validate database connection and migration status."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("🗄️  Validating database connection...")

    try:
        from backend.app.core.db.database import check_database_connection, get_table_info
        from backend.app.core.db.migrations import get_migration_status

        # Database initialization handled by ScriptContext
        logger.info("✅ Database initialized successfully")

        # Check connection
        if check_database_connection():
            logger.info("✅ Database connection successful")
        else:
            logger.error("❌ Database connection failed")
            return False

        # Check migration status
        try:
            migration_status = get_migration_status()
            logger.info(f"📊 Migration status: {migration_status['current_revision'] or 'No migrations applied'}")

            if migration_status.get('pending_migrations'):
                logger.warning(f"⚠️  {len(migration_status['pending_migrations'])} pending migrations")
            else:
                logger.info("✅ Database is up to date")
        except Exception as e:
            logger.warning(f"⚠️  Could not check migration status: {e}")

        # Check table info
        try:
            table_info = get_table_info()
            logger.info(f"📊 Database tables: {table_info.get('documents_count', 0)} documents, {table_info.get('document_chunks_count', 0)} chunks")
            logger.info(f"🔧 pgvector extension: {'✅' if table_info.get('pgvector_installed') else '❌'}")
        except Exception as e:
            logger.warning(f"⚠️  Could not get table info: {e}")

        return True

    except Exception as e:
        logger.error(f"❌ Database validation failed: {e}")
        return False


def validate_logging(logger=None):
    """Validate logging system."""
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("📝 Validating logging system...")

    try:
        from app.core.logging import setup_logging, get_logger

        # Test logger creation
        test_logger = get_logger("test_logger")
        test_logger.info("Test log message")
        logger.info("✅ Logging system working correctly")

        return True

    except Exception as e:
        logger.error(f"❌ Logging validation failed: {e}")
        return False


@script_main
def main():
    """Main validation function."""
    with ScriptContext(__name__) as logger:
        logger.info("🚀 Starting configuration validation...")

        results = {
            "Configuration": validate_configuration(logger),
            "Database": validate_database(logger),
            "Logging": validate_logging(logger),
        }

        # Summary
        logger.info("\n📋 Validation Summary:")
        all_passed = True
        for component, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"  - {component}: {status}")
            if not passed:
                all_passed = False

        if all_passed:
            logger.info("\n🎉 All validations passed! Configuration system is ready.")
            return True
        else:
            logger.error("\n💥 Some validations failed. Please check the configuration.")
            return False


if __name__ == "__main__":
    main()
