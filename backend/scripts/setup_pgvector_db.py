#!/usr/bin/env python
"""
PostgreSQL + pgvector Setup Script

This script sets up PostgreSQL with the pgvector extension and creates the necessary
tables and indexes for the RAG system using the consolidated database functions.

Usage:
    python setup_pgvector.py [--force] [--use-migrations]

Options:
    --force           Force recreation of tables (drops existing tables)
    --use-migrations  Use Alembic migrations instead of direct table creation
"""

import argparse
import sys

# Import common script utilities
from script_utils import (
    setup_script_environment,
    setup_script_logging,
    ScriptContext,
    script_main
)

# Set up environment
setup_script_environment()

# Import database utilities
from backend.app.core.db.database import (
    create_tables,
    reset_database,
    ensure_pgvector_extension,
    get_table_info,
    check_database_connection
)
from backend.app.core.db.migrations import (
    initialize_migrations,
    get_migration_status,
    run_migrations
)

def setup_pgvector(force: bool = False, use_migrations: bool = False, logger=None):
    """
    Set up PostgreSQL with pgvector extension.

    Args:
        force: Whether to force recreation of tables
        use_migrations: Whether to use Alembic migrations
        logger: Logger instance to use
    """
    if logger is None:
        logger = setup_script_logging(__name__)

    logger.info("Setting up PostgreSQL with pgvector extension")

    try:
        # Check database connection (init_db is handled by ScriptContext)
        if not check_database_connection():
            logger.error("Database connection failed")
            return False

        # Ensure pgvector extension is available
        ensure_pgvector_extension()

        if use_migrations:
            logger.info("Using Alembic migrations for setup")

            # Check migration status
            status = get_migration_status()
            logger.info(f"Migration status: {status}")

            if force:
                logger.warning("Force flag with migrations - this will reset the database")
                reset_database(confirm=True)

            # Run migrations
            if initialize_migrations():
                logger.info("Database setup completed using migrations")
            else:
                logger.error("Migration setup failed")
                return False

        else:
            logger.info("Using direct table creation")

            if force:
                logger.warning("Force recreating database tables")
                reset_database(confirm=True)
            else:
                # Create tables if they don't exist
                create_tables()

        # Display table information
        table_info = get_table_info()
        logger.info("Database setup completed successfully")
        logger.info(f"Documents: {table_info.get('documents_count', 0)}")
        logger.info(f"Document chunks: {table_info.get('document_chunks_count', 0)}")
        logger.info(f"pgvector installed: {table_info.get('pgvector_installed', False)}")

        return True

    except Exception as e:
        logger.error(f"Error setting up PostgreSQL + pgvector: {e}")
        return False

@script_main
def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Set up PostgreSQL with pgvector extension")
    parser.add_argument("--force", action="store_true", help="Force recreation of tables")
    parser.add_argument("--use-migrations", action="store_true", help="Use Alembic migrations")
    args = parser.parse_args()

    with ScriptContext(__name__) as logger:
        success = setup_pgvector(force=args.force, use_migrations=args.use_migrations, logger=logger)
        if success:
            logger.info("✅ PostgreSQL setup completed successfully!")
            return True
        else:
            logger.error("❌ PostgreSQL setup failed!")
            return False

if __name__ == "__main__":
    main()