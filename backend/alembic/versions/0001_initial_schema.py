"""Initial schema with documents table and vector support

Revision ID: 0001
Revises:
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create initial schema with pgvector support."""

    # Enable pgvector extension
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")

    # Create documents table
    op.create_table(
        'documents',
        sa.Column('id', sa.String(64), primary_key=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.VECTOR(1536), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True)
    )

    # Create indexes
    op.create_index(
        'documents_embedding_idx',
        'documents',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )

    op.create_index(
        'documents_content_fts',
        'documents',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )

    op.create_index(
        'documents_metadata_idx',
        'documents',
        ['metadata'],
        postgresql_using='gin'
    )


def downgrade() -> None:
    """Drop initial schema."""
    op.drop_index('documents_metadata_idx')
    op.drop_index('documents_content_fts')
    op.drop_index('documents_embedding_idx')
    op.drop_table('documents')
    op.execute("DROP EXTENSION IF EXISTS vector")
