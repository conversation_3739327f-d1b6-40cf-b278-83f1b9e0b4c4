"""Add orchestration tables

Revision ID: c4d505593ad7
Revises: 0001
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c4d505593ad7'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add orchestration tables."""
    
    # Create test embeddings table
    op.create_table(
        'test_embeddings',
        sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.VECTOR(1536), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), server_default=sa.text("'{}'::jsonb"), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('test_embeddings_pkey'))
    )
    
    # Create test vector store table
    op.create_table(
        'test_vector_store',
        sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('embedding', postgresql.VECTOR(1536), nullable=True),
        sa.Column('metadata', postgresql.JSONB(), server_default=sa.text("'{}'::jsonb"), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('test_vector_store_pkey'))
    )
    
    # Create indexes
    op.create_index(
        'test_embeddings_embedding_idx',
        'test_embeddings',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )
    
    op.create_index(
        'test_vector_store_embedding_idx',
        'test_vector_store',
        ['embedding'],
        postgresql_using='ivfflat',
        postgresql_with={'lists': 100},
        postgresql_ops={'embedding': 'vector_cosine_ops'}
    )
    
    op.create_index(
        'test_embeddings_content_fts',
        'test_embeddings',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )
    
    op.create_index(
        'test_vector_store_content_fts',
        'test_vector_store',
        [sa.text("to_tsvector('english', content)")],
        postgresql_using='gin'
    )
    
    op.create_index(
        'test_embeddings_metadata_idx',
        'test_embeddings',
        ['metadata'],
        postgresql_using='gin'
    )
    
    op.create_index(
        'test_vector_store_metadata_idx',
        'test_vector_store',
        ['metadata'],
        postgresql_using='gin'
    )


def downgrade() -> None:
    """Drop orchestration tables."""
    op.drop_index('test_vector_store_metadata_idx')
    op.drop_index('test_embeddings_metadata_idx')
    op.drop_index('test_vector_store_content_fts')
    op.drop_index('test_embeddings_content_fts')
    op.drop_index('test_vector_store_embedding_idx')
    op.drop_index('test_embeddings_embedding_idx')
    op.drop_table('test_vector_store')
    op.drop_table('test_embeddings')
