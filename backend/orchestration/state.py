"""🧠 LangGraph State Model

This module defines the core "shared state model" used throughout the multi-agent orchestration system.
In LangGraph, `state` is a mutable Pydantic object passed between nodes (steps) to track task progress,
agent outputs, context, and orchestration flow.

Each node in the graph (e.g., planning, execution) reads and mutates this state to reflect its evolving
understanding and decisions.

📦 This file provides the following classes:
- LangGraphState (BaseModel): Orchestration-wide shared state, passed between LangGraph nodes.
- Task (BaseModel): A unit of agent work with type, status, dependencies, and metadata.
- TaskType (Enum): Categories of task intent (e.g., REASONING, ACTION).
- TaskStatus (Enum): Lifecycle of a task (e.g., PENDING, COMPLETED).
- OrchestrationStep (Enum): High-level orchestration stages (PLANNING → DONE).
"""

from __future__ import annotations

import uuid
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set, TYPE_CHECKING

from pydantic import BaseModel, ConfigDict, Field, model_validator
from .tracer import TraceEvent
import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

if TYPE_CHECKING:
    from .memory import GlobalMemory

logger = logging.getLogger(__name__)

class OrchestrationStep(str, Enum):
    """Defines the major execution phases (i.e., nodes) in LangGraph-based multi-agent orchestration."""
    PLANNING = "PLANNING"  # Initial user query → Task list generation
    TASK_ASSIGNMENT = "TASK_ASSIGNMENT"  # Assign agents to task
    TASK_EXECUTION = "TASK_EXECUTION"  # Run agent(s) on a task
    REVIEW = "REVIEW"  # Sanity check for potential improvement iteration´
    DONE = "DONE" # Final orchestration state


class TaskType(str, Enum):
    """Describes the intent or behavior category of a task assigned to an agent."""
    QUERY_DECOMPOSITION = "QUERY_DECOMPOSITION"
    REASONING = "REASONING"
    COMMUNICATION = "COMMUNICATION"
    TOOLS = "TOOLS"
    ACTION = "ACTION"
    EVALUATION = "EVALUATION"


class TaskStatus(str, Enum):
    """Tracks the progress of individual tasks"""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class Task(BaseModel):
    """Defines a unit of work (i.e., task) for an agent to perform"""
    task_id: str = Field(default_factory=lambda: f"task_{uuid.uuid4().hex[:8]}")
    description: str = Field(default="")
    task_type: TaskType = Field(default=TaskType.REASONING)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    dependencies: Set[str] = Field(default_factory=set)  # IDs of tasks that must finish before this task can run; set ensures unique IDs
    assigned_agent: Optional[str] = None
    output: Optional[Any] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)  # Additional metadata for task enrichment and tracking

    model_config = ConfigDict(use_enum_values=True)  # Serialize enums as values (e.g., "PENDING") instead of Enum objects (e.g., TaskStatus.PENDING) for non-pydantic use-cases


class LangGraphState(BaseModel):
    """State object passed between LangGraph nodes to coordinate multi-agent tasks and context."""
    user_input: str = Field(default="") # User prompt
    context: Dict[str, Any] = Field(default_factory=dict)  # Shared state across tasks; default_factory uses fresh dict per instance to avoid mutation bugs 
    coordinator_plan_id: Optional[str] = None

    tasks: List[Task] = Field(default_factory=list) # List of tasks to be executed; default empty list
    current_task_id: Optional[str] = None # ID of the current task being processed
    current_step: OrchestrationStep = Field(default=OrchestrationStep.PLANNING) # Set default starting step to PLANNING

    trace: List[Union[TraceEvent, dict, str]] = Field(default_factory=list) # List of trace events; default empty list

    agent_outputs: Dict[str, Any] = Field(default_factory=dict) # Dictionary of agent outputs; default empty dict
    tool_outputs: Dict[str, Any] = Field(default_factory=dict)  # Dictionary of tool outputs; default empty dict

    state_id: Optional[str] = None

    model_config = ConfigDict(
        extra='allow',  # Accept and store fields not explicitly defined in LangGraphState
        arbitrary_types_allowed=True,  # Allow non-standard types (e.g., custom classes like TraceEvent)
        frozen=False,  # Controls mutability; False = mutable (i.e., allow state updates during execution)
        use_enum_values=True  # Serialize enums as values (e.g., "PENDING") instead of Enum objects (e.g., TaskStatus.PENDING) for non-pydantic use-cases
    )

    def get_task_by_id(self, task_id: str) -> Optional[Task]:
        """Get a task by its ID."""
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None

    def is_terminal(self) -> bool:
        """Check if the orchestration has reached its end."""
        return self.current_step == OrchestrationStep.DONE

    def get_pending_tasks(self) -> List[Task]:
        """Get all tasks with PENDING status."""
        return [task for task in self.tasks if task.status == TaskStatus.PENDING]

    def get_completed_tasks(self) -> List[Task]:
        """Get all tasks with COMPLETED status."""
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]

    @model_validator(mode="after")
    def check_current_task_id(self) -> LangGraphState:
        """Ensure the current_task_id, if set, refers to an existing task."""
        if self.current_task_id and not any(task.task_id == self.current_task_id for task in self.tasks):
            raise ValueError(f"Invalid current_task_id: {self.current_task_id} not found in tasks")
        return self

    @classmethod
    async def from_db(cls, state_id: str) -> Optional[LangGraphState]:
        """Load state from the database."""
        from .db import load_state
        return await load_state(state_id)

    async def save(self, db: Optional[AsyncSession] = None) -> str:
        """Save state to the database.
        
        Args:
            db: Optional AsyncSession to use. If not provided, a new session will be created.
        
        Returns:
            str: The ID of the saved state
        """
        from .db import save_state
        self.state_id = await save_state(self, db=db)
        return self.state_id

    def inject_into_context(self, memory: 'GlobalMemory') -> LangGraphState:
        """Inject memory into the state context."""
        # Get recent memories for context
        recent_memories = memory.retrieve_memory(
            state_id=self.state_id,
            limit=10,
            query_embedding=None
        )

        # Format memories for context
        memory_context = []
        for mem in recent_memories:
            memory_context.append({
                "id": mem.memory_id,
                "timestamp": mem.timestamp.isoformat(),
                "step": mem.current_step.value,
                "content": mem.content,
                "agent": mem.agent,
                "task_id": mem.task_id,
                "metadata": mem.metadata,
                "tags": mem.tags
            })

        # Update context with memories
        self.context["memory"] = memory_context
        return self
    