"""
Orchestration module initialization.
Exposes all public components and establishes import order to prevent circular dependencies.
"""

# Core types and enums first (no dependencies)
from .state import (
    TaskType,
    TaskStatus,
    OrchestrationStep,
)

# Models and data structures
from .state import (
    Task,
    LangGraphState,
)

# Database layer
from .db import (
    save_state,
    load_state,
    save_task,
    load_tasks,
    save_memory,
    load_memory,
    search_memory_by_vector,
)

# Memory management
from .memory import (
    MemoryUnits,
    GlobalMemory,
)

# Tracing and monitoring
from .tracer import (
    TraceEvent,
)

# Planner
from .planner import (
    Planner,
)

"""
__all__ defines the public interface of this module when imported with `from app.orchestration import *`.
It also signals which components are intended for external use, helping with clarity and auto-completion.
"""
__all__ = [
    # Core types
    "TaskType",
    "TaskStatus",
    "OrchestrationStep",
    
    # Models
    "Task",
    "LangGraphState",
    
    # Database operations
    "save_state",
    "load_state",
    "save_task",
    "load_tasks",
    "save_memory",
    "load_memory",
    "search_memory_by_vector",
    
    # Memory management
    "MemoryUnits",
    "GlobalMemory",
    
    # Tracing
    "TraceEvent",
    
    # Planner
    "Planner",
]