"""
Persistence layer for orchestration state, tasks, and memory using PostgreSQL + pgvector.
"""
import logging
from typing import Optional, List, Dict, Any, TYPE_CHECKING
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from backend.app.core.db.database import get_async_db_session
from backend.app.core.db.models import (
    OrchestrationState, OrchestrationTask, OrchestrationMemory, TaskDependency
)
from backend.orchestration.state import LangGraphState, Task, OrchestrationStep
from backend.orchestration.memory import MemoryUnits
from backend.app.core.types import Document, SearchResult
from backend.rag.utils.timeout_retry import knowledge_base_operation
from backend.app.core.metrics import metrics

if TYPE_CHECKING:
    pass

logger = logging.getLogger(__name__)

# --- STATE ---
async def save_state(state: 'LangGraphState', db: Optional[AsyncSession] = None) -> str:
    """Persist LangGraphState to the database.
    
    Args:
        state: The LangGraphState instance to save
        db: Optional AsyncSession to use. If not provided, a new session will be created.
    
    Returns:
        str: The ID of the saved state
    """
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _save_state_internal(state, session)
        return await _save_state_internal(state, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to save orchestration state: {e}")
        raise

async def _save_state_internal(state: 'LangGraphState', db: AsyncSession) -> str:
    """Internal implementation of state saving.
    
    Args:
        state: The LangGraphState instance to save
        db: The AsyncSession to use
    
    Returns:
        str: The ID of the saved state
    """
    current_step_value = state.current_step.value if hasattr(state.current_step, 'value') else state.current_step
    db_state = OrchestrationState(
        id=state.id if hasattr(state, 'id') else None,
        user_input=state.user_input,
        context=state.context,
        coordinator_plan_id=state.coordinator_plan_id,
        current_step=current_step_value,
    )
    db.add(db_state)
    await db.flush()
    state_id = db_state.id
    await db.commit()
    return state_id

async def load_state(state_id: str, db: Optional[AsyncSession] = None) -> Optional['LangGraphState']:
    """Load LangGraphState from the database."""
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _load_state_internal(state_id, session)
        return await _load_state_internal(state_id, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to load orchestration state: {e}")
        raise

async def _load_state_internal(state_id: str, db: AsyncSession) -> Optional['LangGraphState']:
    """Internal implementation of state loading."""
    result = await db.execute(
        select(OrchestrationState).options(
            selectinload(OrchestrationState.tasks),
            selectinload(OrchestrationState.memory_units)
        ).filter_by(id=state_id)
    )
    db_state = result.scalar_one_or_none()
    if not db_state:
        return None
    
    tasks = [Task(
        task_id=task.id,
        description=task.description,
        task_type=task.task_type,
        status=task.status,
        dependencies=set(task.dependencies),
        assigned_agent=task.assigned_agent,
        output=task.output,
        metadata=task.task_metadata or {}
    ) for task in db_state.tasks]
    
    return LangGraphState(
        id=db_state.id,
        user_input=db_state.user_input,
        context=db_state.context or {},
        coordinator_plan_id=db_state.coordinator_plan_id,
        tasks=tasks,
        current_task_id=None,
        current_step=OrchestrationStep(db_state.current_step),
    )

# --- TASKS ---
async def save_task(task: 'Task', state_id: str, db: Optional[AsyncSession] = None) -> str:
    """Persist a Task to the database."""
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _save_task_internal(task, state_id, session)
        return await _save_task_internal(task, state_id, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to save orchestration task: {e}")
        raise

async def _save_task_internal(task: 'Task', state_id: str, db: AsyncSession) -> str:
    """Internal implementation of task saving."""
    db_task = OrchestrationTask(
        id=task.task_id,
        state_id=state_id,
        description=task.description,
        task_type=task.task_type,
        status=task.status,
        assigned_agent=task.assigned_agent,
        output=task.output,
        task_metadata=task.metadata or {}
    )
    db.add(db_task)
    await db.commit()
    return db_task.id

async def load_tasks(state_id: str, db: Optional[AsyncSession] = None) -> List['Task']:
    """Load all tasks for a given state."""
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _load_tasks_internal(state_id, session)
        return await _load_tasks_internal(state_id, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to load orchestration tasks: {e}")
        raise

async def _load_tasks_internal(state_id: str, db: AsyncSession) -> List['Task']:
    """Internal implementation of tasks loading."""
    result = await db.execute(
        select(OrchestrationTask).filter_by(state_id=state_id)
    )
    db_tasks = result.scalars().all()
    return [Task(
        task_id=task.id,
        description=task.description,
        task_type=task.task_type,
        status=task.status,
        dependencies=set(),  # You may want to load dependencies
        assigned_agent=task.assigned_agent,
        output=task.output,
        metadata=task.task_metadata or {}
    ) for task in db_tasks]

# --- MEMORY ---
async def save_memory(memory: 'MemoryUnits', state_id: str, embedding: Optional[List[float]] = None, db: Optional[AsyncSession] = None) -> str:
    """Persist a MemoryUnit to the database, including its embedding."""
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _save_memory_internal(memory, state_id, embedding, session)
        return await _save_memory_internal(memory, state_id, embedding, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to save orchestration memory: {e}")
        raise

async def _save_memory_internal(memory: 'MemoryUnits', state_id: str, embedding: Optional[List[float]], db: AsyncSession) -> str:
    """Internal implementation of memory saving."""
    db_memory = OrchestrationMemory(
        state_id=state_id,
        content=memory.content,
        agent=memory.agent,
        task_id=memory.task_id,
        current_step=memory.current_step.value,
        memory_metadata=memory.metadata or {},
        tags=memory.tags,
    )
    db.add(db_memory)
    await db.commit()
    
    if embedding is not None:
        vector_str = f"[{','.join(str(x) for x in embedding)}]"
        await db.execute(
            text("UPDATE orchestration_memory SET embedding = :embedding WHERE id = :id"),
            {"embedding": vector_str, "id": db_memory.id}
        )
        await db.commit()
    return db_memory.id

async def load_memory(state_id: str, limit: int = 10, db: Optional[AsyncSession] = None) -> List['MemoryUnits']:
    """Load recent MemoryUnits for a given state."""
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _load_memory_internal(state_id, limit, session)
        return await _load_memory_internal(state_id, limit, db)
    except SQLAlchemyError as e:
        logger.error(f"Failed to load orchestration memory: {e}")
        raise

async def _load_memory_internal(state_id: str, limit: int, db: AsyncSession) -> List['MemoryUnits']:
    """Internal implementation of memory loading."""
    result = await db.execute(
        select(OrchestrationMemory)
        .filter_by(state_id=state_id)
        .order_by(OrchestrationMemory.created_at.desc())
        .limit(limit)
    )
    db_memories = result.scalars().all()
    return [MemoryUnits(
        memory_id=mem.id,
        timestamp=mem.created_at,
        current_step=OrchestrationStep(mem.current_step),
        content=mem.content,
        agent=mem.agent,
        task_id=mem.task_id,
        metadata=mem.memory_metadata or {},
        tags=mem.tags or []
    ) for mem in db_memories]

@knowledge_base_operation
async def search_memory_by_vector(
    query_embedding: List[float],
    limit: int = 10,
    db: Optional[AsyncSession] = None
) -> List[SearchResult]:
    """
    Search memory using vector similarity.
    
    Args:
        query_embedding: Query embedding vector
        limit: Maximum number of results
        db: Optional database session
        
    Returns:
        List of search results with scores
    """
    try:
        if db is None:
            async with get_async_db_session() as session:
                return await _search_memory_by_vector_internal(query_embedding, limit, session)
        return await _search_memory_by_vector_internal(query_embedding, limit, db)
    except SQLAlchemyError as e:
        metrics.record_failure("memory_vector_search", str(e))
        logger.error(f"Failed to search memory by vector: {e}")
        raise

async def _search_memory_by_vector_internal(
    query_embedding: List[float],
    limit: int,
    db: AsyncSession
) -> List[SearchResult]:
    """Internal implementation of vector search."""
    try:
        # Convert embedding to string format
        vector_str = f"[{','.join(str(x) for x in query_embedding)}]"
        
        # Execute vector similarity search
        result = await db.execute(
            text("""
                SELECT 
                    id, content, agent, current_step, task_id,
                    memory_metadata, tags, embedding, created_at,
                    1 - (embedding <=> :query_embedding) AS score
                FROM orchestration_memory
                ORDER BY embedding <-> :query_embedding
                LIMIT :limit
            """),
            {"query_embedding": vector_str, "limit": limit}
        )
        
        # Process results
        rows = result.fetchall()
        results = []
        
        for row in rows:
            results.append({
                "document": {
                    "id": row.id,
                    "content": row.content,
                    "metadata": {
                        "agent": row.agent,
                        "current_step": row.current_step,
                        "task_id": row.task_id,
                        "memory_metadata": row.memory_metadata,
                        "tags": row.tags,
                        "created_at": row.created_at.isoformat() if row.created_at else None
                    }
                },
                "score": float(row.score) if row.score is not None else 0.0,
                "source": "vector"
            })
        
        metrics.record_success("memory_vector_search")
        return results
        
    except Exception as e:
        metrics.record_failure("memory_vector_search", str(e))
        logger.error(f"Error in vector search: {str(e)}")
        raise 