"""Integration tests for vector search functionality."""

import asyncio
import pytest
import pytest_asyncio
import uuid
from datetime import datetime
from typing import List

from backend.orchestration.memory import GlobalMemory
from backend.orchestration.state import LangGraphState, OrchestrationStep
from backend.orchestration.db import save_memory, load_memory, search_memory_by_vector
from backend.orchestration.planner import Planner, TaskType, TaskStatus
from backend.orchestration.sandbox.test_infrastructure import _TestStateManager, test_state

# Test constants
TEST_EMBEDDING_DIM = 768
TEST_USER_INPUT = "test user input"

@pytest.mark.asyncio
async def test_basic_state(test_state: _TestStateManager):
    """Test basic state management functionality."""
    # Just check that state_id exists and is a valid UUID
    assert test_state.state_id is not None
    uuid_obj = uuid.UUID(test_state.state_id)
    assert str(uuid_obj) == test_state.state_id

@pytest.mark.asyncio
async def test_vector_search_accuracy(test_state: _TestStateManager):
    """Test vector similarity search accuracy with proper state isolation."""
    memory = GlobalMemory()
    planner = Planner()

    # Create base embedding and more distinct embeddings
    base_embedding = [1.0] * TEST_EMBEDDING_DIM
    similar_embedding = [0.8] * TEST_EMBEDDING_DIM
    different_embedding = [0.0] * TEST_EMBEDDING_DIM

    # Add memories with different embeddings
    memory_ids = []
    for i, (content, embedding) in enumerate([
        ("Base memory content", base_embedding),
        ("Similar memory content", similar_embedding),
        ("Different memory content", different_embedding)
    ]):
        memory_id = await memory.add_memory(
            content=content,
            agent="test_agent",
            current_step=OrchestrationStep.PLANNING,
            task_id=f"task_{i}",
            metadata={"test": f"memory_{i}"},
            tags=["test_tag"],
            embedding=embedding,
            state_id=test_state.state_id
        )
        memory_ids.append(memory_id)

    # Search for the most similar memory to base_embedding
    results = await memory.search_memory_by_vector(
        query_embedding=base_embedding,
        state_id=test_state.state_id,
        top_k=3
    )
    assert len(results) == 3
    # The first result should be the base memory (score 1.0)
    assert results[0]["score"] >= results[1]["score"] >= results[2]["score"]
    assert results[0]["content"] == "Base memory content"

async def main():
    """Run all tests with proper state isolation."""
    print("Running vector search accuracy test...")
    async with test_state() as state:
        await test_vector_search_accuracy(state)

if __name__ == "__main__":
    asyncio.run(main()) 