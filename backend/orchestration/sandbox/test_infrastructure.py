"""Test infrastructure for orchestration system tests.

This module provides utilities for managing test state, database transactions,
and test isolation in a production-ready manner.
"""

import asyncio
import uuid
import pytest_asyncio
import pytest
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker

from backend.orchestration.db import get_async_db_session
from backend.orchestration.state import LangGraphState, OrchestrationStep
from backend.app.core.db.database import close_async_db, init_async_db

@pytest_asyncio.fixture(scope="function")
async def db_session():
    """Create a database session for each test function."""
    await init_async_db()
    async with get_async_db_session() as session:
        yield session
    await close_async_db()

class _TestStateManager:
    """Manages test state and provides isolation for tests."""
    def __init__(self, state_id: Optional[str] = None):
        self._state_id: Optional[str] = state_id
        self._session: Optional[AsyncSession] = None

    @property
    def state_id(self) -> str:
        if not self._state_id:
            raise RuntimeError("Test state not initialized. Call setup() first.")
        return self._state_id

    @property
    def session(self) -> AsyncSession:
        if not self._session:
            raise RuntimeError("Database session not initialized. Call setup() first.")
        return self._session

@asynccontextmanager
async def _test_state_ctx(session: AsyncSession) -> AsyncGenerator[_TestStateManager, None]:
    """Context manager for test state management."""
    state_id = str(uuid.uuid4())
    manager = _TestStateManager(state_id=state_id)
    manager._session = session
    
    state = LangGraphState(
        id=state_id,
        user_input="test_input",
        tasks=[],
        current_step=OrchestrationStep.PLANNING
    )
    
    try:
        await state.save(db=session)
        await session.commit()
        yield manager
    except Exception:
        await session.rollback()
        raise

@pytest_asyncio.fixture
async def test_state(db_session: AsyncSession) -> AsyncGenerator[_TestStateManager, None]:
    """Fixture for test state management."""
    async with _test_state_ctx(db_session) as manager:
        yield manager

async def run_test_with_isolation(test_func, session: AsyncSession):
    """Run a test function with proper isolation."""
    async with _test_state_ctx(session) as state:
        await test_func(state)

@pytest.mark.asyncio
async def test_state_management(test_state: _TestStateManager):
    """Test that state management works correctly."""
    # Verify state was created
    assert test_state.state_id is not None
    
    # Verify session is available
    assert test_state.session is not None
    
    # Verify we can load the state
    from backend.orchestration.db import load_state
    loaded_state = await load_state(test_state.state_id, db=test_state.session)
    assert loaded_state is not None
    assert loaded_state.id == test_state.state_id
    assert loaded_state.user_input == "test_input"
    assert loaded_state.current_step == OrchestrationStep.PLANNING 