"""
Test script for PostgreSQL + pgvector integration with orchestration components.
"""
import asyncio
import logging
from typing import List, Dict, Any
from .. import (
    LangGraphState,
    OrchestrationStep,
    Planner,
    GlobalMemory,
    save_state,
    load_state,
    save_task,
    load_tasks,
    save_memory,
    search_memory_by_vector,
    TaskType,
    TaskStatus
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_integration():
    # Create a new LangGraphState
    state = LangGraphState(
        user_input="Test user input",
        context={"test": "context"},
        coordinator_plan_id="test_plan",
        current_step=OrchestrationStep(OrchestrationStep.PLANNING),
        tasks=[],
    )
    logger.info("Created new LangGraphState")

    # Save the state to the database
    state_id = await state.save()
    logger.info(f"Saved state to database with ID: {state_id}")

    # Create a Planner and add a task
    planner = Planner()
    task = await planner.create_task(
        state=state,
        description="Test task",
        task_type=TaskType.REASONING,
        dependencies=["dependency1"],
        metadata={"test": "metadata"},
    )
    logger.info(f"Created task: {task.task_id}")

    # Create GlobalMemory and add memory with an embedding
    memory = GlobalMemory()
    # Use a dynamic 768-dimensional embedding for testing
    embedding = [float(i % 10) / 10.0 for i in range(768)]
    memory_id = await memory.add_memory(
        content="Test memory content",
        agent="test_agent",
        current_step=OrchestrationStep(OrchestrationStep.PLANNING),
        task_id=task.task_id,
        metadata={"test": "memory_metadata"},
        tags=["test_tag"],
        embedding=embedding,
        state_id=state_id,
    )
    logger.info(f"Added memory with ID: {memory_id}")

    # Load the state from the database
    loaded_state = await LangGraphState.from_db(state_id)
    if loaded_state:
        logger.info(f"Loaded state from database: {loaded_state.user_input}")
        logger.info(f"Loaded tasks: {[t.task_id for t in loaded_state.tasks]}")
    else:
        logger.error("Failed to load state from database")

    # Retrieve memory using vector search
    search_results = await search_memory_by_vector(embedding, limit=10)
    logger.info(f"Vector search results: {search_results}")

if __name__ == "__main__":
    asyncio.run(test_integration()) 