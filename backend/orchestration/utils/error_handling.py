"""
Error handling utilities for orchestration components.

This module provides centralized error handling functionality
used across different orchestration components.
"""

import logging
import functools
from typing import Callable, Any, Optional, TypeVar, cast
from datetime import datetime

from ..memory import GlobalMemory, MemoryUnits
from ..state import OrchestrationStep

logger = logging.getLogger(__name__)

T = TypeVar('T')

def handle_orchestration_errors(
    agent_name: str,
    memory: Optional[GlobalMemory] = None,
    task_id: Optional[str] = None,
    current_step: Optional[OrchestrationStep] = None
):
    """
    Decorator for handling orchestration errors consistently.
    
    Args:
        agent_name: Name of the agent or component
        memory: Optional memory instance for error logging
        task_id: Optional task ID for error context
        current_step: Optional current orchestration step
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_msg = f"{agent_name} error: {str(e)}"
                logger.error(error_msg)
                
                if memory:
                    error_memory = MemoryUnits(
                        content=error_msg,
                        agent=agent_name,
                        task_id=task_id or "unknown",
                        current_step=current_step or OrchestrationStep.TASK_EXECUTION,
                        tags=["error", "execution_error"],
                        metadata={
                            "timestamp": datetime.now().isoformat(),
                            "error_type": type(e).__name__
                        }
                    )
                    memory.add_memory(error_memory)
                
                raise
                
        return cast(Callable[..., T], wrapper)
    return decorator

def log_error_to_memory(
    memory: GlobalMemory,
    error: Exception,
    agent_name: str,
    task_id: str,
    current_step: OrchestrationStep,
    additional_context: Optional[dict] = None
) -> None:
    """
    Log an error to memory with context.
    
    Args:
        memory: Memory instance
        error: The error that occurred
        agent_name: Name of the agent
        task_id: Task ID where error occurred
        current_step: Current orchestration step
        additional_context: Optional additional context
    """
    error_memory = MemoryUnits(
        content=f"Error in {agent_name}: {str(error)}",
        agent=agent_name,
        task_id=task_id,
        current_step=current_step,
        tags=["error", "execution_error"],
        metadata={
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "context": additional_context or {}
        }
    )
    memory.add_memory(error_memory) 