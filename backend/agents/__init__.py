"""
Agents Package

This package contains the implementation of various agents in the multi-agent orchestration system:
- BaseAgent: Abstract base class for all agents
- CoCEOAgent: Primary orchestrator agent for planning and coordination
- FinanceAgent: Domain-specific agent for financial tasks (TODO)
- MarketingAgent: Domain-specific agent for marketing tasks (TODO)
"""

# Import base agent
from .base import BaseAgent

# Import specific agents
from .co_ceo import CoCEOAgent
from .marketing import MarketingAgent
from .finance import FinanceAgent

# Define the list of exported symbols
__all__ = [
    "BaseAgent",
    "CoCEOAgent",
    "MarketingAgent",
    "FinanceAgent",
    # TODO: Add when implemented
    # "FinanceAgent",
    # "MarketingAgent",
]