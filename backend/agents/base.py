"""
BaseAgent class for multi-agent orchestration system.

This class provides a reusable base structure for all agents involved in task execution.
The class handles:
-> Running assigned tasks (async def run(task, context))
    -> 1. Type safety checks
    -> 2. Extracting key task information
    -> 3. Retrieving relevant memory context (RAG)
    -> 4. Building LLM messages
    -> 5. Interacting with the LLM to get response
    -> 6. Storing result in memory (RAG)
    -> 7. Injecting outputs into the LangGraph state
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from ..rag.llm import RAGLLMAdapter

from ..orchestration.memory import GlobalMemory
from ..orchestration.state import LangGraphState, Task
from ..rag import KnowledgeBaseService

logger = logging.getLogger(__name__)


class BaseAgent:
    """
    Base class for all agents in the orchestration system.

    Args:
        llm_adapter (RAGLLMAdapter): Adapter for calling the underlying LLM.
        memory (GlobalMemory, optional): Shared memory interface.
        knowledge_base (KnowledgeBaseService, optional): RAG knowledge base service.
    """

    def __init__(
        self,
        llm_adapter: RAGLLMAdapter,

        memory: Optional[GlobalMemory] = None,
        knowledge_base: Optional[KnowledgeBaseService] = None,
    ):
        self.llm_adapter = llm_adapter

        self.memory = memory
        self.knowledge_base = knowledge_base
        self._rag_cache: Dict[str, Tuple[datetime, List[Dict[str, Any]]]] = {}
        self._cache_ttl = 300  # 5 minutes cache TTL

    def build_system_prompt(self, task_description: str, agent_name: str) -> str:
        """
        Generate a system prompt that defines the agent's behavior for the task.

        Can be overridden by subclasses (e.g., FinanceAgent, MarketingAgent).

        Args:
            task_description: Description of the current task.
            agent_name: Name of the agent running the task.

        Returns:
            A system message string.
        """
        return f"You are the agent '{agent_name}' working on the following task: {task_description}"

    async def _get_rag_context(self, query: str, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get relevant context from RAG system with caching.
        
        Args:
            query: The search query
            force_refresh: Whether to force a cache refresh
            
        Returns:
            List of relevant documents from RAG
        """
        if not self.knowledge_base:
            return []

        # Check cache first
        if not force_refresh and query in self._rag_cache:
            timestamp, results = self._rag_cache[query]
            if (datetime.now() - timestamp).total_seconds() < self._cache_ttl:
                return results

        try:
            async with asyncio.timeout(30):  # 30 second timeout
                results = await self.knowledge_base.search(
                    query=query,
                    limit=5,
                    search_type="hybrid"
                )
                # Update cache
                self._rag_cache[query] = (datetime.now(), results)
                return results
        except asyncio.TimeoutError:
            logger.warning(f"RAG search timed out for query: {query}")
            return []
        except Exception as e:
            logger.exception(f"RAG search failed for query {query}: {e}")
            return []

    async def _store_in_memory(self, content: str, task: Task, context: LangGraphState) -> None:
        """
        Store task results in memory system.
        
        Args:
            content: Content to store
            task: The task being executed
            context: Current LangGraph state
        """
        if not self.memory:
            return

        try:
            await self.memory.add_memory(
                content=content,
                agent=task.assigned_agent,
                current_step=context.current_step,
                task_id=task.task_id,
                metadata={
                    "task_type": task.task_type,
                    "task_description": task.description,
                    "timestamp": datetime.now().isoformat()
                },
                tags=["task_output", task.task_type]
            )
        except Exception as e:
            logger.exception(f"Failed to store memory for task {task.task_id}: {e}")

    async def run(self, task: Task, context: LangGraphState) -> str:
        """
        Run a task using the agent's logic and optional memory retrieval.
        Always call the LLM for every task.
        """
        # --- 1. Type safety checks ---
        if not isinstance(task, Task):
            raise TypeError("Expected a `Task` object")
        if not isinstance(context, LangGraphState):
            raise TypeError("Expected a `LangGraphState` object")

        # --- 2. Extract task info ---
        task_id = task.task_id
        task_type = task.task_type
        task_description = task.description
        assigned_agent = task.assigned_agent or "unknown"

        logger.info("[LLM] Running task %s (%s) for agent %s", task_id, task_type, assigned_agent)
        logger.debug("[LLM] Task description: %s", task_description)
        logger.debug("[LLM] Current LangGraph step: %s", context.current_step)

        # --- 3. Get RAG context ---
        rag_results = await self._get_rag_context(task_description)
        if rag_results:
            context.context["rag_results"] = rag_results
            logger.debug(f"[RAG] Retrieved {len(rag_results)} relevant documents")

        # --- 4. Build messages ---
        system_prompt = self.build_system_prompt(task_description, assigned_agent)
        messages = [
            {"role": "system", "content": system_prompt}
        ]

        # Add RAG context if available
        if rag_results:
            rag_context = "\n".join([f"- {r['content']}" for r in rag_results])
            messages.append({
                "role": "system",
                "content": f"Relevant context from knowledge base:\n{rag_context}"
            })

        # --- 5. Call LLM ---
        try:
            response = await self.llm_adapter.chat(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
        except Exception as e:
            logger.error(f"LLM call failed for task {task_id}: {e}")
            response = f"Error executing task: {str(e)}"

        # --- 6. Store in memory ---
        await self._store_in_memory(response, task, context)

        return response
