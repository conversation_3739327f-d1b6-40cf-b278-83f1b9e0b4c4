"""
CoCEO Agent - Primary Orchestrator

The CoCEO agent acts as the primary orchestrator that:
- Interprets high-level user input
- Generates orchestration plans using the planner
- Can directly use tools (RAG, web search) to inform decisions
- Coordinates with other agents
- Makes strategic decisions about task delegation
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
from pydantic import BaseModel, Field
import uuid

# Import base agent
from .base import BaseAgent

# Import orchestration components
from ..orchestration.state import Task, TaskType, LangGraphState, OrchestrationStep
from ..orchestration.planner import OrchestrationPlanner
from ..orchestration.memory import GlobalMemory, MemoryUnits
from ..orchestration.utils.shared_services import ContextService

# Import LLM components
from ..rag.llm import RAGLLMAdapter

# Import RAG components
from ..rag import KnowledgeBaseService, initialize_knowledge_base_service

logger = logging.getLogger(__name__)

# ============================================================================
# Core Class Models
# ============================================================================

class AgentCapability(BaseModel):
    """Defines capabilities and constraints for an agent."""
    name: str
    capabilities: List[str]
    max_concurrent_tasks: int = 3
    domain_keywords: List[str] = Field(default_factory=list)

class Decision(BaseModel):
    """Structured model for LLM decisions."""
    choice: Union[int, str]
    reasoning: str
    confidence: float = Field(ge=0.0, le=1.0, default=0.7)
    alternatives: Optional[List[str]] = None

class CoCEOAgent(BaseAgent):
    """
    CoCEO Agent - Primary orchestrator for multi-agent workflows.
    
    Responsibilities:
    - High-level planning and task decomposition
    - Strategic decision making
    - Direct tool usage for informed planning
    - Coordination with other agents
    - Plan evaluation and adjustment
    """
    
    def __init__(
        self,
        llm_adapter: RAGLLMAdapter,
        memory: Optional[GlobalMemory] = None,
        planner: Optional[OrchestrationPlanner] = None,
        knowledge_base: Optional[KnowledgeBaseService] = None,
        agent_name: str = "CoCEO"
    ):
        super().__init__(llm_adapter, memory, knowledge_base)
        
        # CoCEO-specific components
        self.planner = planner  # Will be initialized if None
        self.knowledge_base = knowledge_base  # Will be initialized if None
        self.context_service = None  # Will be initialized with services
        
        # CoCEO identity and capabilities
        self.agent_name = agent_name
        self.capabilities = [
            "strategic_planning",
            "task_decomposition", 
            "agent_coordination",
            "direct_tool_usage",
            "plan_evaluation"
        ]
        
        # Initialize agent capabilities
        self.agent_capabilities: Dict[str, AgentCapability] = {}
        
        # Register default agents
        self._register_default_agents()

        self.current_task: Optional[Task] = None

    def _register_default_agents(self):
        """Register default agents with their capabilities."""
        self.register_agent(
            "CoCEO",
            capabilities=self.capabilities,
            max_concurrent_tasks=5,
            domain_keywords=["strategy", "planning", "coordination"]
        )
        
        self.register_agent(
            "Finance",
            capabilities=["financial_analysis", "budgeting", "cost_control"],
            max_concurrent_tasks=3,
            domain_keywords=["finance", "cost", "budget", "revenue", "profit"]
        )
        
        self.register_agent(
            "Marketing",
            capabilities=["campaign_planning", "brand_management", "customer_engagement"],
            max_concurrent_tasks=3,
            domain_keywords=["marketing", "campaign", "brand", "customer", "promotion"]
        )
    
    def register_agent(
        self,
        name: str,
        capabilities: List[str],
        max_concurrent_tasks: int = 3,
        domain_keywords: Optional[List[str]] = None
    ):
        """Register a new agent with its capabilities."""
        self.agent_capabilities[name] = AgentCapability(
            name=name,
            capabilities=capabilities,
            max_concurrent_tasks=max_concurrent_tasks,
            domain_keywords=domain_keywords or []
        )
        logger.info(f"Registered agent: {name} with {len(capabilities)} capabilities")
    
    async def _get_rag_context(self) -> Dict[str, Any]:
        """
        Get relevant context from RAG system for the current task.
        
        Returns:
            Dictionary containing RAG results and metadata
        """
        if not self.knowledge_base:
            return {"snippets": [], "metadata": []}

        try:
            async with asyncio.timeout(30):  # 30 second timeout
                results = await self.knowledge_base.search(
                    query=self.current_task.description if hasattr(self, 'current_task') else "",
                    limit=5,
                    search_type="hybrid"
                )
                
                return {
                    "snippets": [r["content"] for r in results],
                    "metadata": [r.get("metadata", {}) for r in results],
                    "scores": [r.get("score", 0.0) for r in results]
                }
        except asyncio.TimeoutError:
            logger.warning("RAG context retrieval timed out after 30 seconds")
            return {"snippets": [], "metadata": []}
        except Exception as e:
            logger.exception(f"RAG context retrieval failed: {e}")
            return {"snippets": [], "metadata": []}

    def build_system_prompt(self, task_description: str, _agent_name: str) -> str:
        """Build system prompt using template engine."""
        rag_context = self._get_rag_context() if self.knowledge_base else None
        rag_snippets = "\n".join(f"- {s}" for s in rag_context.get("snippets", [])) if rag_context else ""

        return (
            f"You are the CoCEO agent in a multi-agent orchestration system.\n"
            f"Your task: {task_description}\n\n"
            f"You must act strategically, coordinate with other agents, and ensure efficient execution.\n"
            f"Your core capabilities: {', '.join(self.capabilities)}.\n"
            f"Available agents: {', '.join(self.agent_capabilities.keys())}.\n"
            f"{'Relevant context from knowledge base:\n' + rag_snippets if rag_snippets else ''}"
        )

    async def _initialize_services(self):
        """Initialize required services if not already initialized."""
        try:
            if not self.knowledge_base:
                self.knowledge_base = await initialize_knowledge_base_service()
                logger.info("Initialized knowledge base service")

            if not self.planner:
                self.planner = OrchestrationPlanner(
                    llm_adapter=self.llm_adapter,
                    knowledge_base=self.knowledge_base
                )
                logger.info("Initialized orchestration planner")

            if not self.context_service:
                self.context_service = ContextService(
                    llm_adapter=self.llm_adapter,
                    knowledge_base=self.knowledge_base
                )
                # Add memory to context service if available
                if self.memory:
                    self.context_service.memory = self.memory
                logger.info("Initialized context service")

        except Exception as e:
            logger.exception(f"Failed to initialize services: {e}")
            raise

    async def run(self, task: Task, context: LangGraphState) -> str:
        """
        Run a task using the CoCEO's capabilities.
        
        Args:
            task: The task to execute
            context: Current LangGraph state
            
        Returns:
            Task output string
        """
        try:
            # Store current task for RAG context
            self.current_task = task
            
            # Initialize services if needed
            await self._initialize_services()
            
            # Get RAG context
            rag_context = await self._get_rag_context()
            if rag_context["snippets"]:
                context.context["rag_results"] = rag_context
                logger.debug(f"[RAG] Retrieved {len(rag_context['snippets'])} relevant documents")
            
            # Build messages with RAG context
            system_prompt = self.build_system_prompt(task.description, self.agent_name)
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Add RAG context if available
            if rag_context["snippets"]:
                rag_snippets = "\n".join(f"- {s}" for s in rag_context["snippets"])
                messages.append({
                    "role": "system",
                    "content": f"Relevant context from knowledge base:\n{rag_snippets}"
                })
            
            # Call LLM
            try:
                response = await self.llm_adapter.chat(
                    messages=messages,
                    temperature=0.3,
                    max_tokens=1000
                )
            except Exception as e:
                logger.exception(f"LLM call failed: {e}")
                response = f"Error executing task: {str(e)}"
            
            # Store in memory
            if self.memory:
                await self._store_in_memory(response, task, context)
            
            return response
            
        except Exception as e:
            logger.exception(f"Task execution failed: {e}")
            return f"Error executing task: {str(e)}"
        finally:
            # Cleanup
            if hasattr(self, 'current_task'):
                delattr(self, 'current_task')

    async def _store_in_memory(self, content: str, task: Task, context: LangGraphState) -> None:
        """Store task results in memory system."""
        if not self.memory:
            return

        try:
            await self.memory.add_memory(
                content=content,
                agent=self.agent_name,
                current_step=context.current_step,
                task_id=task.task_id,
                metadata={
                    "task_type": task.task_type,
                    "task_description": task.description,
                    "timestamp": datetime.now().isoformat(),
                    "rag_context": context.context.get("rag_results", {})
                },
                tags=["task_output", task.task_type, "co_ceo"]
            )
        except Exception as e:
            logger.exception(f"Failed to store memory for task {task.task_id}: {e}")

    async def plan_execution(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Task], str]:
        """
        Generate a comprehensive execution plan for the user's request.
        
        This is the CoCEO's primary responsibility - taking high-level user input
        and breaking it down into actionable tasks for the multi-agent system.
        
        Args:
            user_input: The user's request / prompt 
            context: Additional context (previous conversations, user preferences, etc.)
            
        Returns:
            Tuple of (List of Task objects, Analysis string explaining the plan)
        """
        logger.info(f"[CoCEO] Starting execution planning for: {user_input[:100]}...")
        
        try:
            # Initialize services if needed
            await self._initialize_services()
            
            # Store planning decision in memory
            if self.memory:
                planning_memory = MemoryUnits(
                    content=f"Starting execution planning for: {user_input}",
                    agent=self.agent_name,
                    task_id="planning_start",
                    current_step=OrchestrationStep.PLANNING,
                    tags=["plan_step", "strategic"]
                )
                self.memory.add_memory(planning_memory)
            
            # Use the planner to generate structured tasks
            tasks, analysis = await self.planner.generate_plan(user_input, context)
            
            # Add CoCEO-specific enrichment to the plan
            enriched_tasks = await self._enrich_plan_with_strategic_context(
                tasks, user_input, context
            )
            
            # Store planning result in memory
            if self.memory:
                result_memory = MemoryUnits(
                    memory_id=str(uuid.uuid4()),
                    timestamp=datetime.now(),
                    content=f"Generated plan with {len(enriched_tasks)} tasks: {analysis[:200]}...",
                    agent=self.agent_name,
                    task_id="planning_complete",
                    current_step=OrchestrationStep.PLANNING,
                    tags=["planning", "execution_plan"]
                )
                await self.memory.add_memory(
                    content=result_memory.content,
                    agent=result_memory.agent,
                    current_step=result_memory.current_step,
                    task_id=result_memory.task_id,
                    metadata={"memory_id": result_memory.memory_id},
                    tags=result_memory.tags
                )
            
            logger.info(f"[CoCEO] Generated plan with {len(enriched_tasks)} tasks")
            return enriched_tasks, analysis
            
        except Exception as e:
            logger.exception(f"[CoCEO] Error in execution planning: {e}")
            # Simple fallback plan
            fallback_task = Task(
                description=f"Fallback processing for: {user_input}",
                task_type=TaskType.REASONING,
                assigned_agent="CoCEO"
            )
            return [fallback_task], f"Fallback plan generated due to error: {e}"

    async def gather_strategic_context(
        self,
        query: str,
        _context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gather strategic context using shared context service.

        This allows the CoCEO to make informed decisions by accessing
        relevant information before planning or coordinating.

        Args:
            query: The information need
            _context: Additional context for the search (unused, kept for compatibility)

        Returns:
            Dictionary containing gathered context from various sources
        """
        logger.info(f"[CoCEO] Gathering strategic context for: {query}")

        try:
            # Initialize services if needed
            await self._initialize_services()

            # Use shared context service with memory integration
            memory_filters = {
                "agent": self.agent_name,
                "any_tags": ["strategic", "plan_step"],
                "limit": 5
            }

            strategic_context = await self.context_service.gather_context(
                query=query,
                limit=5,
                include_memory=True,
                memory_filters=memory_filters
            )

            logger.debug(f"[CoCEO] Retrieved context with {len(strategic_context.get('rag_results', []))} RAG results and {len(strategic_context.get('memory_context', []))} memory items")
            return strategic_context

        except Exception as e:
            logger.exception(f"[CoCEO] Error gathering strategic context: {e}")
            return {
                "rag_results": [],
                "memory_context": [],
                "timestamp": datetime.now().isoformat()
            }

    async def coordinate_agents(
        self, 
        plan: List[Task], 
        available_agents: Optional[List[str]] = None,
        context: Optional[LangGraphState] = None
    ) -> Dict[str, Any]:
        """
        Coordinate task assignment and execution across multiple agents.
        
        This method handles the strategic coordination of other agents,
        making decisions about task assignment, dependencies, and execution order.
        
        Args:
            plan: List of tasks to be executed
            available_agents: List of available agent names (uses default if None)
            context: Current orchestration state
            
        Returns:
            Dictionary containing coordination decisions and assignments
        """
        if available_agents is None:
            available_agents = list(self.agent_capabilities.keys())
            
        logger.info(f"[CoCEO] Coordinating {len(plan)} tasks across {len(available_agents)} agents")
        
        coordination_result = {
            "assignments": {},
            "execution_order": [],
            "dependencies": {}
        }
        
        try:
            # Analyze tasks and make strategic assignments
            for task in plan:
                # Determine best agent for this task
                assigned_agent = await self._determine_optimal_agent(
                    task, available_agents, context
                )
                
                # Update task assignment
                task.assigned_agent = assigned_agent
                coordination_result["assignments"][task.task_id] = assigned_agent
                
                # Track dependencies
                if task.dependencies:
                    coordination_result["dependencies"][task.task_id] = list(task.dependencies)
                
                # Store assignment decision in memory
                if self.memory:
                    assignment_memory = MemoryUnits(
                        memory_id=str(uuid.uuid4()),
                        timestamp=datetime.now(),
                        content=f"Assigned task {task.task_id} ({task.task_type}) to {assigned_agent}: {task.description[:100]}",
                        agent=self.agent_name,
                        task_id=task.task_id,
                        current_step=OrchestrationStep.TASK_ASSIGNMENT,
                        tags=["agent_assignment", "coordination"]
                    )
                    await self.memory.add_memory(
                        content=assignment_memory.content,
                        agent=assignment_memory.agent,
                        current_step=assignment_memory.current_step,
                        task_id=assignment_memory.task_id,
                        metadata={"memory_id": assignment_memory.memory_id},
                        tags=assignment_memory.tags
                    )
            
            # Determine execution order based on dependencies
            coordination_result["execution_order"] = self._calculate_execution_order(plan)
            
            # Log coordination notes internally
            self._log_coordination_notes(plan, available_agents, context)
            
            logger.info(f"[CoCEO] Coordination complete: {len(coordination_result['assignments'])} assignments")
            return coordination_result
            
        except Exception as e:
            logger.exception(f"[CoCEO] Error in agent coordination: {e}")
            return coordination_result

    async def make_strategic_decision(
        self,
        decision_context: Dict[str, Any],
        options: List[Dict[str, Any]],
        context: Optional[LangGraphState] = None,
        temperature: float = 0.3
    ) -> Dict[str, Any]:
        """
        Make strategic decisions using LLM reasoning and available context.

        This method handles high-level strategic decisions that require
        the CoCEO's oversight and reasoning capabilities.

        Args:
            decision_context: Context for the decision
            options: Available options to choose from
            context: Current orchestration state
            temperature: LLM temperature for decision making

        Returns:
            Dictionary containing the decision and reasoning
        """
        logger.info(f"[CoCEO] Making strategic decision with {len(options)} options")

        try:
            # Gather additional context if needed
            strategic_context = await self.gather_strategic_context(
                decision_context.get("query", ""),
                decision_context
            )

            # Build decision prompt
            messages = [
                {
                    "role": "system",
                    "content": (
                        f"You are making a strategic decision with the following context:\n"
                        f"{decision_context}\n\n"
                        f"Available options:\n"
                        + "\n".join(f"{i+1}. {opt}" for i, opt in enumerate(options))
                        + "\n\nPlease analyze and choose the best option."
                    )
                }
            ]

            # Get LLM reasoning
            decision_response = await self.llm_adapter.chat(
                messages=messages,
                temperature=temperature,
                max_tokens=1000
            )

            # Parse and structure the decision
            decision_result = await self._parse_strategic_decision(
                decision_response, options, decision_context
            )

            # Store decision in memory for future reference
            if self.memory:
                decision_memory = MemoryUnits(
                    memory_id=str(uuid.uuid4()),
                    timestamp=datetime.now(),
                    content=f"Strategic decision: {decision_result['choice']} | Reasoning: {decision_result['reasoning'][:200]}...",
                    agent=self.agent_name,
                    task_id="strategic_decision",
                    current_step=context.current_step if context else OrchestrationStep.PLANNING,
                    tags=["strategic", "decision", "coordination"],
                    metadata={
                        "memory_id": str(uuid.uuid4()),
                        "decision_choice": str(decision_result['choice']),
                        "confidence": decision_result['confidence'],
                        "full_reasoning": decision_result['reasoning']
                    }
                )
                await self.memory.add_memory(
                    content=decision_memory.content,
                    agent=decision_memory.agent,
                    current_step=decision_memory.current_step,
                    task_id=decision_memory.task_id,
                    metadata=decision_memory.metadata,
                    tags=decision_memory.tags
                )

            logger.info(f"[CoCEO] Strategic decision made: {decision_result.get('choice', 'unknown')}")
            return decision_result

        except Exception as e:
            logger.exception(f"[CoCEO] Error in strategic decision making: {e}")
            return {
                "choice": options[0] if options else {},
                "reasoning": f"Fallback decision due to error: {e}",
                "confidence": 0.1
            }

    async def _parse_strategic_decision(
        self,
        decision_response: str,
        options: List[Dict[str, Any]],
        decision_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse LLM response into structured decision result."""
        try:
            # Try to parse JSON response
            if "{" in decision_response and "}" in decision_response:
                # Extract JSON from response
                start_idx = decision_response.find("{")
                end_idx = decision_response.rfind("}") + 1
                json_str = decision_response[start_idx:end_idx]

                # Parse into structured Decision model
                try:
                    decision_data = Decision.model_validate_json(json_str)
                    return {
                        "choice": decision_data.choice,
                        "reasoning": decision_data.reasoning,
                        "confidence": decision_data.confidence,
                        "alternatives": decision_data.alternatives,
                        "raw_response": decision_response,
                        "decision_context": decision_context.get("description", "No context")
                    }
                except Exception as e:
                    logger.warning(f"Failed to parse decision JSON: {e}")
                    # Fallback to first option
                    return {
                        "choice": options[0] if options else {},
                        "reasoning": f"Fallback to first option due to parsing error: {e}",
                        "confidence": 0.1,
                        "alternatives": options[1:] if len(options) > 1 else [],
                        "raw_response": decision_response,
                        "decision_context": decision_context.get("description", "No context")
                    }

        except Exception as e:
            logger.exception(f"[CoCEO] Error parsing strategic decision: {e}")
            return {
                "choice": options[0] if options else {},
                "reasoning": f"Error parsing decision: {e}",
                "confidence": 0.1,
                "alternatives": "Error in parsing",
                "raw_response": decision_response,
                "decision_context": decision_context.get("description", "No context")
            }

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    async def _enrich_plan_with_strategic_context(
        self,
        tasks: List[Task],
        user_input: str,
        _context: Optional[Dict[str, Any]]
    ) -> List[Task]:
        """Add CoCEO-specific strategic context to tasks using shared context service."""
        logger.debug(f"[CoCEO] Enriching plan with strategic context for {len(tasks)} tasks")

        try:
            # Use shared context service to get enrichment context
            enrichment_context = await self.context_service.gather_context(
                query=user_input,
                limit=3
            )

            # Add context to all tasks
            if enrichment_context.get("rag_results"):
                for task in tasks:
                    task.metadata.update({
                        "rag_context": {
                            "snippets": [r.get("content", "") for r in enrichment_context["rag_results"]],
                            "metadata": [r.get("metadata", {}) for r in enrichment_context["rag_results"]]
                        }
                    })
        except Exception as e:
            logger.exception(f"Error getting enrichment context: {e}")

        # Add basic metadata to tasks
        for task in tasks:
            task.metadata.update({
                "enriched_by": self.agent_name,
                "enrichment_timestamp": datetime.now().isoformat()
            })

        logger.debug(f"[CoCEO] Enriched {len(tasks)} tasks with strategic context")
        return tasks

    async def _determine_optimal_agent(
        self,
        task: Task,
        available_agents: List[str],
        _context: Optional[LangGraphState]
    ) -> str:
        """Determine the best agent for a specific task."""
        try:
            # First, try rule-based mapping
            if task.task_type in self.agent_capabilities:
                preferred_agent = task.task_type
                if preferred_agent in available_agents:
                    logger.debug(f"[CoCEO] Assigned {task.task_id} to {preferred_agent} via rule-based mapping")
                    return preferred_agent

            # Check for domain-specific keywords in task description
            task_desc_lower = task.description.lower()

            # Check agent capabilities
            for agent, capability in self.agent_capabilities.items():
                if agent not in available_agents:
                    continue
                    
                # Check domain keywords
                if any(keyword in task_desc_lower for keyword in capability.domain_keywords):
                    logger.debug(f"[CoCEO] Assigned {task.task_id} to {agent} via capability matching")
                    return agent

            # Default to CoCEO for complex reasoning or coordination tasks
            if "CoCEO" in available_agents:
                logger.debug(f"[CoCEO] Assigned {task.task_id} to CoCEO as default")
                return "CoCEO"

            # Fallback to first available agent
            fallback_agent = available_agents[0] if available_agents else "CoCEO"
            logger.debug(f"[CoCEO] Assigned {task.task_id} to {fallback_agent} as fallback")
            return fallback_agent
            
        except Exception as e:
            logger.exception(f"[CoCEO] Error in agent assignment: {e}")
            return "CoCEO"  # Default to CoCEO on error

    def _calculate_execution_order(self, tasks: List[Task]) -> List[str]:
        """Calculate optimal execution order based on dependencies."""
        logger.debug(f"[CoCEO] Calculating execution order for {len(tasks)} tasks")

        # Simple topological sort based on dependencies
        ordered_tasks = []
        remaining_tasks = tasks.copy()

        while remaining_tasks:
            # Find tasks with no unresolved dependencies
            ready_tasks = [
                task for task in remaining_tasks
                if not task.dependencies or
                all(dep_id in [t.task_id for t in ordered_tasks] for dep_id in task.dependencies)
            ]

            if not ready_tasks:
                # Break circular dependencies by taking the first task
                logger.warning("[CoCEO] Circular dependency detected, breaking with first remaining task")
                ready_tasks = [remaining_tasks[0]]

            # Add ready tasks to order
            for task in ready_tasks:
                ordered_tasks.append(task.task_id)
                remaining_tasks.remove(task)

        logger.debug(f"[CoCEO] Execution order calculated: {ordered_tasks}")
        return ordered_tasks

    def _log_coordination_notes(
        self,
        plan: List[Task],
        available_agents: List[str],
        context: Optional[LangGraphState]
    ):
        """Log coordination notes internally."""
        notes = []

        # Add context information if available
        if context:
            notes.append(f"Orchestration step: {context.current_step}")
            if context.context:
                notes.append(f"Context keys: {list(context.context.keys())}")

        # Analyze task distribution
        agent_task_count = {}
        for task in plan:
            agent = task.assigned_agent or "Unassigned"
            agent_task_count[agent] = agent_task_count.get(agent, 0) + 1

        notes.append(f"Task distribution: {agent_task_count}")

        # Check agent utilization
        unused_agents = [agent for agent in available_agents if agent not in agent_task_count]
        if unused_agents:
            notes.append(f"Unused agents: {unused_agents}")

        # Check dependency complexity
        total_dependencies = sum(len(task.dependencies) for task in plan)
        if total_dependencies > len(plan):
            notes.append(f"High dependency complexity: {total_dependencies} dependencies for {len(plan)} tasks")

        logger.info(f"[CoCEO] Coordination notes: {'; '.join(notes)}")