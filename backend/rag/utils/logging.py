"""
Logging Utilities

This module provides logging utilities for the RAG system.
"""

import logging
import sys
from typing import Optional

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)

def setup_logging(
    level: int = logging.INFO,
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream: Optional[logging.StreamHandler] = None
) -> None:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level
        format: Log message format
        stream: Stream handler (defaults to sys.stdout)
    """
    # Create formatter
    formatter = logging.Formatter(format)
    
    # Create stream handler if not provided
    if stream is None:
        stream = logging.StreamHandler(sys.stdout)
    
    # Configure stream handler
    stream.setFormatter(formatter)
    
    # Get root logger
    root_logger = logging.getLogger()
    
    # Set level
    root_logger.setLevel(level)
    
    # Add handler
    root_logger.addHandler(stream)
    
    # Log setup
    root_logger.info(f"Logging configured with level {logging.getLevelName(level)}") 