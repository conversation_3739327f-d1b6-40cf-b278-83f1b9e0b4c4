"""
Vector Store Implementation

This module provides vector store implementations for document storage and retrieval.
"""

import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Type

from ..app.core.types import Document, SearchResult
from ..app.core.metrics import metrics
from ..app.core.db.database import get_db_context
from ..app.config import get_settings
from .utils.timeout_retry import (
    hybrid_search_operation,
    vector_search_operation,
    knowledge_base_operation
)

logger = logging.getLogger(__name__)


class VectorStore:
    """
    Base vector store interface.
    
    This class defines the interface for vector stores,
    supporting different storage backends (PostgreSQL, FAISS, etc.).
    """

    @knowledge_base_operation
    async def add_document(
        self,
        document: Document,
        **kwargs
    ) -> str:
        """
        Add a document to the store.
        
        Args:
            document: Document to add
            **kwargs: Additional arguments
            
        Returns:
            Document ID
        """
        raise NotImplementedError

    @knowledge_base_operation
    async def update_document(
        self,
        doc_id: str,
        document: Document,
        **kwargs
    ) -> None:
        """
        Update a document in the store.
        
        Args:
            doc_id: Document ID
            document: Updated document
            **kwargs: Additional arguments
        """
        raise NotImplementedError

    @knowledge_base_operation
    async def delete_document(
        self,
        doc_id: str
    ) -> None:
        """
        Delete a document from the store.
        
        Args:
            doc_id: Document ID
        """
        raise NotImplementedError

    @knowledge_base_operation
    async def get_document(
        self,
        doc_id: str
    ) -> Optional[Document]:
        """
        Get a document from the store.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        raise NotImplementedError

    @vector_search_operation
    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for similar documents.
        
        Args:
            query_embedding: Query embedding vector
            limit: Maximum number of results
            filters: Optional metadata filters
            
        Returns:
            List of search results with scores
        """
        raise NotImplementedError


class PgVectorStore(VectorStore):
    """
    PostgreSQL vector store implementation using pgvector.
    
    This implementation uses PostgreSQL with the pgvector extension
    for efficient vector similarity search.
    """

    def __init__(
        self,
        dimension: Optional[int] = None,
        table_name: str = "documents",
        distance_metric: str = "cosine"
    ):
        """
        Initialize the PostgreSQL vector store.
        
        Args:
            dimension: Dimension of embedding vectors
            table_name: Name of the table to store documents
            distance_metric: Distance metric for similarity search
        """
        self.dimension = dimension or get_settings().VECTOR_DIMENSION
        self.table_name = table_name
        self.distance_metric = distance_metric
        
        # Initialize database
        self._init_db()

    def _init_db(self) -> None:
        """Initialize database table and indexes."""
        with get_db_context() as db:
            try:
                # Create table if not exists
                db.execute(f"""
                    CREATE TABLE IF NOT EXISTS {self.table_name} (
                        id TEXT PRIMARY KEY,
                        content TEXT NOT NULL,
                        embedding vector({self.dimension}),
                        metadata JSONB,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    );
                    
                    CREATE INDEX IF NOT EXISTS {self.table_name}_embedding_idx 
                    ON {self.table_name} 
                    USING ivfflat (embedding vector_cosine_ops)
                    WITH (lists = 100);
                """)
                db.commit()
                logger.info(f"Initialized {self.table_name} table")
            except Exception as e:
                db.rollback()
                logger.error(f"Error initializing database: {str(e)}")
                raise

    async def add_document(
        self,
        document: Document,
        **kwargs
    ) -> str:
        """Add a document to the store."""
        try:
            doc_id = str(uuid.uuid4())
            
            with get_db_context() as db:
                db.execute(f"""
                    INSERT INTO {self.table_name} (id, content, embedding, metadata)
                    VALUES (:id, :content, :embedding::vector, :metadata)
                """, {
                    "id": doc_id,
                    "content": document["content"],
                    "embedding": document["embedding"],
                    "metadata": json.dumps(document["metadata"])
                })
                db.commit()
            
            metrics.record_success("vector_store_add")
            return doc_id
            
        except Exception as e:
            metrics.record_failure("vector_store_add", str(e))
            logger.error(f"Error adding document: {str(e)}")
            raise

    async def update_document(
        self,
        doc_id: str,
        document: Document,
        **kwargs
    ) -> None:
        """Update a document in the store."""
        try:
            with get_db_context() as db:
                db.execute(f"""
                    UPDATE {self.table_name}
                    SET content = :content,
                        embedding = :embedding::vector,
                        metadata = :metadata,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id
                """, {
                    "id": doc_id,
                    "content": document["content"],
                    "embedding": document["embedding"],
                    "metadata": json.dumps(document["metadata"])
                })
                db.commit()
            
            metrics.record_success("vector_store_update")
            
        except Exception as e:
            metrics.record_failure("vector_store_update", str(e))
            logger.error(f"Error updating document: {str(e)}")
            raise

    async def delete_document(
        self,
        doc_id: str
    ) -> None:
        """Delete a document from the store."""
        try:
            with get_db_context() as db:
                db.execute(f"""
                    DELETE FROM {self.table_name}
                    WHERE id = :id
                """, {"id": doc_id})
                db.commit()
            
            metrics.record_success("vector_store_delete")
            
        except Exception as e:
            metrics.record_failure("vector_store_delete", str(e))
            logger.error(f"Error deleting document: {str(e)}")
            raise

    async def get_document(
        self,
        doc_id: str
    ) -> Optional[Document]:
        """Get a document from the store."""
        try:
            with get_db_context() as db:
                result = db.execute(f"""
                    SELECT id, content, embedding, metadata
                    FROM {self.table_name}
                    WHERE id = :id
                """, {"id": doc_id})
                row = result.fetchone()
                
                if not row:
                    return None
                
                metrics.record_success("vector_store_get")
                return {
                    "id": row.id,
                    "content": row.content,
                    "embedding": row.embedding,
                    "metadata": json.loads(row.metadata)
                }
            
        except Exception as e:
            metrics.record_failure("vector_store_get", str(e))
            logger.error(f"Error getting document: {str(e)}")
            raise

    async def search(
        self,
        query_embedding: List[float],
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar documents."""
        try:
            # Build query
            query = f"""
                SELECT id, content, metadata,
                       1 - (embedding <=> :query_embedding::vector) as score
                FROM {self.table_name}
            """
            
            params = {"query_embedding": query_embedding}
            
            # Add filters
            if filters:
                filter_conditions = []
                for key, value in filters.items():
                    param_name = f"filter_{key}"
                    filter_conditions.append(f"metadata->>'{key}' = :{param_name}")
                    params[param_name] = str(value)
                
                if filter_conditions:
                    query += " WHERE " + " AND ".join(filter_conditions)
            
            # Add ordering and limit
            query += " ORDER BY score DESC LIMIT :limit"
            params["limit"] = limit
            
            # Execute query
            with get_db_context() as db:
                result = db.execute(query, params)
                rows = result.fetchall()
                
                # Process results
                results = []
                for row in rows:
                    results.append({
                        "document": {
                            "id": row.id,
                            "content": row.content,
                            "metadata": json.loads(row.metadata)
                        },
                        "score": float(row.score),
                        "source": "vector"
                    })
                
                metrics.record_success("vector_store_search")
                return results
            
        except Exception as e:
            metrics.record_failure("vector_store_search", str(e))
            logger.error(f"Error searching documents: {str(e)}")
            raise


# Factory function
async def get_vector_store(
    store_type: str = "pgvector",
    **kwargs
) -> VectorStore:
    """
    Get a vector store instance.
    
    Args:
        store_type: Type of vector store
        **kwargs: Additional arguments
        
    Returns:
        Vector store instance
        
    Raises:
        ValueError: If store type is not supported
    """
    store_type = store_type.lower()
    
    if store_type == "pgvector":
        return PgVectorStore(**kwargs)
    else:
        raise ValueError(f"Unsupported vector store type: {store_type}")