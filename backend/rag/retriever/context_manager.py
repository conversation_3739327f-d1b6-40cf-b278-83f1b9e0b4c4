"""
Context Manager

This module provides context management functionality for the RAG system.
Focuses on essential context optimization and token management.
"""

from typing import Dict, List, Any, Optional
from ...app.core.logging import get_logger
from ..utils.timeout_retry import hybrid_search_operation
import tiktoken

logger = get_logger(__name__)

class ContextManager:
    """
    Manages context optimization for retrieved documents.
    Focuses on token limit management and basic relevance scoring.
    """
    
    def __init__(
        self,
        llm_adapter: Any,
        max_tokens: int = 4096,
        token_buffer: int = 1000,
        min_threshold: float = 0.7,
        max_retries: int = 3,
        timeout_seconds: int = 30
    ):
        self.llm_adapter = llm_adapter
        self.max_tokens = max_tokens
        self.token_buffer = token_buffer
        self.min_threshold = min_threshold
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        
        # Initialize tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    @hybrid_search_operation
    async def optimize_context(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Optimize context by:
        1. Scoring documents for relevance
        2. Sorting by relevance score
        3. Applying token limit
        """
        try:
            # Score documents
            scored_docs = await self._score_documents(query, documents)
            
            # Sort by relevance score
            sorted_docs = sorted(
                scored_docs,
                key=lambda x: x.get("relevance_score", 0),
                reverse=True
            )
            
            # Apply token limit
            selected_docs = await self._apply_token_limit(sorted_docs)
            
            return selected_docs[:limit]
            
        except Exception as e:
            logger.error(f"Error optimizing context: {str(e)}")
            # Return original documents if optimization fails
            return documents[:limit]

    @hybrid_search_operation
    async def _score_documents(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Score documents for relevance to query."""
        try:
            scored_docs = []
            
            for doc in documents:
                # Calculate relevance score
                relevance_score = await self._score_relevance(doc, query)
                
                # Add score to document
                doc["relevance_score"] = relevance_score
                scored_docs.append(doc)
            
            return scored_docs
            
        except Exception as e:
            logger.error(f"Error scoring documents: {str(e)}")
            return documents

    @hybrid_search_operation
    async def _apply_token_limit(
        self,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply token limit to selected documents."""
        try:
            selected_docs = []
            total_tokens = 0
            
            for doc in documents:
                # Count tokens in document
                doc_tokens = len(self.tokenizer.encode(doc["text"]))
                
                # Check if adding document would exceed limit
                if total_tokens + doc_tokens > self.max_tokens - self.token_buffer:
                    break
                
                # Add document
                selected_docs.append(doc)
                total_tokens += doc_tokens
            
            return selected_docs
            
        except Exception as e:
            logger.error(f"Error applying token limit: {str(e)}")
            return documents

    @hybrid_search_operation
    async def fit_to_context_window(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Ensure documents fit within the context window.

        Args:
            query: The query text
            documents: The retrieved documents

        Returns:
            Documents that fit within the context window
        """
        # Estimate query tokens
        query_tokens = await self._count_tokens(query)

        # Calculate available tokens for documents
        available_tokens = self.max_tokens - query_tokens - self.token_buffer

        # Estimate tokens for each document
        documents_with_tokens = []
        for doc in documents:
            doc_tokens = await self._count_tokens(doc.get("text", ""))
            documents_with_tokens.append({
                **doc,
                "tokens": doc_tokens
            })

        # Sort by score (descending)
        documents_with_tokens.sort(key=lambda x: x.get("score", 0), reverse=True)

        # Fit documents to context window
        fitted_documents = []
        total_tokens = 0

        for doc in documents_with_tokens:
            doc_tokens = doc["tokens"]

            if total_tokens + doc_tokens <= available_tokens:
                # Document fits entirely
                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_documents.append(fitted_doc)
                total_tokens += doc_tokens
            elif total_tokens < available_tokens:
                # Document needs truncation
                truncated_text = await self._truncate_text(
                    doc.get("text", ""),
                    available_tokens - total_tokens
                )

                fitted_doc = {k: v for k, v in doc.items() if k != "tokens"}
                fitted_doc["text"] = truncated_text
                fitted_doc["truncated"] = True
                fitted_documents.append(fitted_doc)

                # Update total tokens
                truncated_tokens = await self._count_tokens(truncated_text)
                total_tokens += truncated_tokens
            else:
                # No more space available
                break

        return fitted_documents

    @hybrid_search_operation
    async def _count_tokens(self, text: str) -> int:
        """Count tokens in text."""
        try:
            return len(self.tokenizer.encode(text))
        except Exception as e:
            logger.error(f"Error counting tokens: {str(e)}")
            return 0

    @hybrid_search_operation
    async def _truncate_text(
        self,
        text: str,
        max_tokens: int
    ) -> str:
        """Truncate text to fit within token limit."""
        try:
            # Encode text
            tokens = self.tokenizer.encode(text)
            
            # Truncate if needed
            if len(tokens) > max_tokens:
                tokens = tokens[:max_tokens]
            
            # Decode back to text
            return self.tokenizer.decode(tokens)
            
        except Exception as e:
            logger.error(f"Error truncating text: {str(e)}")
            return text

    @hybrid_search_operation
    async def _score_relevance(
        self,
        document: Dict[str, Any],
        query: str
    ) -> float:
        """Score document relevance to query."""
        try:
            prompt = f"""
            Score the relevance of this document to the query:
            
            Query: {query}
            
            Document: {document['text']}
            
            Return a score between 0 and 1 where:
            0 = Completely irrelevant
            1 = Perfectly relevant
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring relevance: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _score_recency(self, document: Dict[str, Any]) -> float:
        """Score document recency."""
        try:
            prompt = f"""
            Score the recency of this document:
            
            Document: {document['text']}
            Timestamp: {document.get('timestamp', 'Unknown')}
            
            Return a score between 0 and 1 where:
            0 = Very outdated
            1 = Very recent
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring recency: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _score_completeness(self, document: Dict[str, Any]) -> float:
        """Score document completeness."""
        try:
            prompt = f"""
            Score the completeness of this document:
            
            Document: {document['text']}
            
            Return a score between 0 and 1 where:
            0 = Very incomplete
            1 = Very complete
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring completeness: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _score_density(self, document: Dict[str, Any]) -> float:
        """Score document information density."""
        try:
            prompt = f"""
            Score the information density of this document:
            
            Document: {document['text']}
            
            Return a score between 0 and 1 where:
            0 = Very low information density
            1 = Very high information density
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error scoring density: {str(e)}")
            return 0.0

    @hybrid_search_operation
    async def _prioritize_documents(
        self,
        documents: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """Prioritize documents based on multiple factors."""
        try:
            prioritized_docs = []
            
            for doc in documents:
                # Calculate priority scores
                relevance_score = await self._score_relevance(doc, query)
                recency_score = await self._score_recency(doc)
                completeness_score = await self._score_completeness(doc)
                density_score = await self._score_density(doc)
                
                # Calculate total priority score
                priority_score = (
                    relevance_score * self.weights["relevance"] +
                    recency_score * self.weights["recency"] +
                    completeness_score * self.weights["completeness"] +
                    density_score * self.weights["density"]
                )
                
                doc["priority_score"] = priority_score
                prioritized_docs.append(doc)
            
            # Sort by priority score
            prioritized_docs.sort(
                key=lambda x: x["priority_score"],
                reverse=True
            )
            
            return prioritized_docs
            
        except Exception as e:
            logger.error(f"Error prioritizing documents: {str(e)}")
            return documents

    async def _adjust_context_size(
        self,
        documents: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """Dynamically adjust context size based on query complexity."""
        try:
            # Calculate query complexity
            complexity = await self._calculate_query_complexity(query)
            
            # Adjust max tokens based on complexity
            adjusted_max_tokens = int(self.max_tokens * complexity)
            
            # Use fit_to_context_window with adjusted size
            return await self.fit_to_context_window(
                query,
                documents
            )
            
        except Exception as e:
            logger.error(f"Error adjusting context size: {str(e)}")
            return documents

    async def _calculate_query_complexity(self, query: str) -> float:
        """Calculate query complexity score."""
        try:
            prompt = f"""
            Calculate the complexity of this query:
            
            Query: {query}
            
            Return a score between 0.5 and 1.5 where:
            0.5 = Very simple query
            1.0 = Average complexity
            1.5 = Very complex query
            
            Consider:
            1. Number of entities
            2. Query intent complexity
            3. Required context depth
            4. Information needs
            """
            
            response = await self.llm_adapter.generate(prompt)
            return float(response)
            
        except Exception as e:
            logger.error(f"Error calculating query complexity: {str(e)}")
            return 1.0

    async def _remove_redundancies(
        self,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Remove redundant information from documents."""
        try:
            if len(documents) < 2:
                return documents
            
            deduped_docs = []
            seen_content = set()
            
            for doc in documents:
                # Check for redundancy
                is_redundant = await self._check_redundancy(
                    doc,
                    seen_content
                )
                
                if not is_redundant:
                    seen_content.add(doc['text'])
                    deduped_docs.append(doc)
            
            return deduped_docs
            
        except Exception as e:
            logger.error(f"Error removing redundancies: {str(e)}")
            return documents

    async def _check_redundancy(
        self,
        document: Dict[str, Any],
        seen_content: set
    ) -> bool:
        """Check if document is redundant with seen content."""
        try:
            for seen in seen_content:
                prompt = f"""
                Check if these documents are redundant:
                
                Document 1: {document['text']}
                Document 2: {seen}
                
                Return a JSON object with:
                1. is_redundant: Boolean indicating if documents are redundant
                2. confidence: Confidence score (0-1)
                """
                
                response = await self.llm_adapter.generate(prompt)
                
                if response["is_redundant"] and response["confidence"] >= self.min_threshold:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking redundancy: {str(e)}")
            return False

    async def _optimize_density(
        self,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Optimize information density of documents."""
        try:
            optimized_docs = []
            
            for doc in documents:
                prompt = f"""
                Optimize the information density of this document:
                
                Document: {doc['text']}
                
                Return an optimized version that:
                1. Maintains all key information
                2. Removes unnecessary details
                3. Improves structure
                4. Increases density
                """
                
                response = await self.llm_adapter.generate(prompt)
                
                doc['text'] = response
                optimized_docs.append(doc)
            
            return optimized_docs
            
        except Exception as e:
            logger.error(f"Error optimizing density: {str(e)}")
            return documents

    async def fit_prompt_to_context_window(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None
    ) -> List[Dict[str, str]]:
        """
        Ensure a prompt fits within the context window.
        
        This method is specifically designed for fitting chat messages
        before sending them to an LLM.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            max_tokens: Optional override for max tokens

        Returns:
            Fitted messages that will fit in the context window
        """
        if max_tokens is None:
            max_tokens = self.max_tokens
            
        # Reserve tokens for the response
        available_tokens = max_tokens - self.token_buffer
        
        # Count tokens in all messages
        total_tokens = 0
        messages_with_tokens = []
        
        for msg in messages:
            msg_tokens = await self._count_tokens(msg.get("content", ""))
            # Add overhead for message format (role, etc.)
            msg_tokens += 4  # Simple approximation for message overhead
            
            messages_with_tokens.append({
                **msg,
                "tokens": msg_tokens
            })
            total_tokens += msg_tokens
            
        # If everything fits, return original messages
        if total_tokens <= available_tokens:
            return messages
            
        # Otherwise, we need to truncate
        fitted_messages = []
        current_tokens = 0
        
        # Always keep the system message if present
        system_messages = [m for m in messages_with_tokens if m.get("role") == "system"]
        for msg in system_messages:
            fitted_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
            current_tokens += msg["tokens"]
            
        # Always keep the most recent user message
        user_messages = [m for m in messages_with_tokens if m.get("role") == "user"]
        if user_messages:
            latest_user_msg = user_messages[-1]
            
            # Check if we need to truncate the user message
            if current_tokens + latest_user_msg["tokens"] > available_tokens:
                # Truncate the user message
                truncated_content = await self._truncate_text(
                    latest_user_msg["content"],
                    available_tokens - current_tokens
                )
                fitted_messages.append({
                    "role": "user",
                    "content": truncated_content
                })
            else:
                fitted_messages.append({
                    "role": "user",
                    "content": latest_user_msg["content"]
                })
                current_tokens += latest_user_msg["tokens"]
                
        # Add as many previous messages as possible, starting from the most recent
        remaining_messages = [
            m for m in messages_with_tokens 
            if m.get("role") != "system" and 
               (not user_messages or m != user_messages[-1])
        ]
        
        # Reverse to process from most recent to oldest
        remaining_messages.reverse()
        
        for msg in remaining_messages:
            if current_tokens + msg["tokens"] <= available_tokens:
                fitted_messages.insert(
                    len([m for m in fitted_messages if m.get("role") == "system"]), 
                    {
                        "role": msg["role"],
                        "content": msg["content"]
                    }
                )
                current_tokens += msg["tokens"]
            else:
                # No more space
                break
                
        return fitted_messages


async def get_context_manager(
    llm_adapter: Any,
    max_tokens: int = 4096,
    token_buffer: int = 1000,
    min_threshold: float = 0.7,
    max_retries: int = 3,
    timeout_seconds: int = 30
) -> ContextManager:
    """
    Get a context manager instance.
    
    Args:
        llm_adapter: The LLM adapter to use for token counting and optimization
        max_tokens: Maximum tokens allowed in the context window
        token_buffer: Buffer tokens to reserve for the query and response
        min_threshold: Minimum confidence threshold for optimizations
        max_retries: Maximum number of retries for operations
        timeout_seconds: Timeout in seconds for operations
        
    Returns:
        ContextManager instance
    """
    return ContextManager(
        llm_adapter=llm_adapter,
        max_tokens=max_tokens,
        token_buffer=token_buffer,
        min_threshold=min_threshold,
        max_retries=max_retries,
        timeout_seconds=timeout_seconds
    ) 