"""
Query Rewriter

This module provides query rewriting functionality for the RAG system.
"""

from typing import Dict, Any, Optional
from ...app.core.logging import get_logger
from ..utils.timeout_retry import hybrid_search_operation
from ..llm import RAGLLMAdapter

logger = get_logger(__name__)

class QueryRewriter:
    """
    Query rewriter for enhancing search queries.
    
    This class provides functionality for:
    1. Query expansion
    2. Query reformulation
    3. Query optimization
    """
    
    def __init__(
        self,
        llm_adapter: RAGLLMAdapter,
        max_retries: int = 3,
        timeout_seconds: int = 30
    ):
        """
        Initialize query rewriter.
        
        Args:
            llm_adapter: LLM adapter for query rewriting
            max_retries: Maximum number of retries
            timeout_seconds: Timeout in seconds
        """
        self.llm_adapter = llm_adapter
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds

    @hybrid_search_operation
    async def rewrite(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Rewrite a query to improve search results.
        
        Args:
            query: Original query
            context: Optional context for rewriting
            
        Returns:
            Rewritten query
        """
        try:
            # Generate rewrite prompt
            prompt = self._generate_rewrite_prompt(query, context)
            
            # Get rewrite from LLM
            rewritten = await self.llm_adapter.generate(prompt)
            
            # Clean and validate rewrite
            cleaned = self._clean_rewrite(rewritten)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"Error rewriting query: {str(e)}")
            return query  # Return original query on error

    def _generate_rewrite_prompt(
        self,
        query: str,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """Generate prompt for query rewriting."""
        prompt = f"""
        Rewrite this search query to improve search results:
        
        Original Query: {query}
        
        """
        
        if context:
            prompt += f"""
            Context:
            - Domain: {context.get('domain', 'General')}
            - Intent: {context.get('intent', 'Information')}
            - Previous Queries: {context.get('previous_queries', [])}
            """
        
        prompt += """
        Guidelines:
        1. Maintain the original intent
        2. Add relevant synonyms
        3. Include domain-specific terms
        4. Keep it concise and clear
        
        Rewritten Query:"""
        
        return prompt

    def _clean_rewrite(self, rewrite: str) -> str:
        """Clean and validate rewritten query."""
        # Remove quotes if present
        rewrite = rewrite.strip('"\'')
        
        # Remove any prompt artifacts
        rewrite = rewrite.replace("Rewritten Query:", "").strip()
        
        # Ensure non-empty
        if not rewrite:
            return "No valid rewrite generated"
        
        return rewrite