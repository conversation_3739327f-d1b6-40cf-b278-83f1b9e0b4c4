"""
Hybrid Retriever

This module provides a hybrid retriever that combines vector and keyword search.
"""

import logging
from typing import Dict, List, Any, Optional

from ...app.core.types import Document, SearchResult
from ...app.core.metrics import metrics
from .base import Retriever
from ..vector_store import VectorStore
from ..embeddings import EmbeddingModel
from ..llm import RAGLLMAdapter
from .context_manager import ContextManager
from .query_rewriter import QueryRewriter
from ..utils.timeout_retry import (
    hybrid_search_operation,
    embedding_operation,
    vector_search_operation,
    keyword_search_operation
)

logger = logging.getLogger(__name__)


class HybridRetriever(Retriever):
    """
    Hybrid retriever combining vector and keyword search.
    
    This retriever uses both semantic and keyword-based search to find
    relevant documents, with configurable weighting between the two approaches.
    Features:
    - Vector similarity search
    - Keyword search
    - Query processing and rewriting
    - Context window management
    - Result combination and reranking
    """

    def __init__(
        self,
        vector_store: VectorStore,
        embedding_model: EmbeddingModel,
        llm_adapter: RAG<PERSON>MAdapter,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        use_reranking: bool = False,
        max_tokens: int = 4000,
        token_buffer: int = 1000
    ):
        """
        Initialize the hybrid retriever.
        
        Args:
            vector_store: Vector store for semantic search
            embedding_model: Model for generating embeddings
            llm_adapter: LLM adapter for query processing
            vector_weight: Weight for vector search results
            keyword_weight: Weight for keyword search results
            use_reranking: Whether to use reranking for search results
            max_tokens: Maximum tokens for context window
            token_buffer: Buffer for token counting
        """
        super().__init__(vector_weight=vector_weight, keyword_weight=keyword_weight)
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.llm_adapter = llm_adapter
        self.use_reranking = use_reranking
        
        # Initialize components
        self.query_rewriter = QueryRewriter(llm_adapter)
        self.context_manager = ContextManager(
            max_tokens=max_tokens,
            token_buffer=token_buffer
        )

    @hybrid_search_operation
    async def search(
        self,
        query: str,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """
        Search for relevant documents.
        
        Args:
            query: Search query
            limit: Maximum number of results
            filters: Optional metadata filters
            
        Returns:
            List of search results with scores
        """
        try:
            # Process and rewrite query
            processed_query = await self._process_query(query)
            
            # Get results from both search methods
            vector_results = await self._vector_search(processed_query, limit * 2, filters)
            keyword_results = await self._keyword_search(processed_query, limit * 2, filters)
            
            # Combine and normalize scores
            combined_results = await self._combine_results(
                vector_results=vector_results,
                keyword_results=keyword_results,
                limit=limit
            )
            
            # Apply reranking if enabled
            if self.use_reranking:
                combined_results = await self._rerank_results(
                    combined_results,
                    processed_query
                )
            
            # Manage context window
            final_results = await self.context_manager.prioritize_documents(
                combined_results,
                processed_query
            )
            
            return final_results[:limit]
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            raise

    @embedding_operation
    async def _process_query(self, query: str) -> str:
        """Process and rewrite query for better retrieval."""
        try:
            # Use query rewriter to enhance query
            rewritten_query = await self.query_rewriter.rewrite(query)
            return rewritten_query.strip()
        except Exception as e:
            logger.warning(f"Query processing failed: {str(e)}, using original query")
            return query

    @vector_search_operation
    async def _vector_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]]
    ) -> List[SearchResult]:
        """Perform vector search."""
        try:
            # Generate query embedding
            query_embedding = await self.embedding_model.embed_document(query)
            
            # Search vector store
            results = await self.vector_store.search(
                query_embedding=query_embedding,
                limit=limit,
                filters=filters
            )
            
            # Normalize scores
            if results:
                max_score = max(r["score"] for r in results)
                for r in results:
                    r["score"] = r["score"] / max_score if max_score > 0 else 0
            
            return results
            
        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            return []

    @keyword_search_operation
    async def _keyword_search(
        self,
        query: str,
        limit: int,
        filters: Optional[Dict[str, Any]]
    ) -> List[SearchResult]:
        """Perform keyword search."""
        try:
            # Use vector store's keyword search if available
            if hasattr(self.vector_store, "keyword_search"):
                results = await self.vector_store.keyword_search(
                    query=query,
                    limit=limit,
                    filters=filters
                )
                
                # Normalize scores
                if results:
                    max_score = max(r["score"] for r in results)
                    for r in results:
                        r["score"] = r["score"] / max_score if max_score > 0 else 0
                
                return results
            return []
            
        except Exception as e:
            logger.error(f"Keyword search failed: {str(e)}")
            return []

    @hybrid_search_operation
    async def _combine_results(
        self,
        vector_results: List[SearchResult],
        keyword_results: List[SearchResult],
        limit: int
    ) -> List[SearchResult]:
        """Combine and normalize results from both search methods."""
        try:
            # Create document ID to result mapping
            combined = {}
            
            # Add vector results
            for result in vector_results:
                doc_id = result["document"]["id"]
                combined[doc_id] = {
                    "document": result["document"],
                    "vector_score": result["score"],
                    "keyword_score": 0.0,
                    "score": result["score"] * self.vector_weight,
                    "source": "vector"
                }
            
            # Add keyword results
            for result in keyword_results:
                doc_id = result["document"]["id"]
                if doc_id in combined:
                    # Combine scores if document exists in both results
                    combined[doc_id]["keyword_score"] = result["score"]
                    combined[doc_id]["score"] += result["score"] * self.keyword_weight
                    combined[doc_id]["source"] = "hybrid"
                else:
                    combined[doc_id] = {
                        "document": result["document"],
                        "vector_score": 0.0,
                        "keyword_score": result["score"],
                        "score": result["score"] * self.keyword_weight,
                        "source": "keyword"
                    }
            
            # Sort by combined score
            sorted_results = sorted(
                combined.values(),
                key=lambda x: x["score"],
                reverse=True
            )
            
            return sorted_results[:limit]
            
        except Exception as e:
            logger.error(f"Error combining results: {str(e)}")
            return []

    @hybrid_search_operation
    async def _rerank_results(
        self,
        results: List[SearchResult],
        query: str
    ) -> List[SearchResult]:
        """Rerank results using LLM for better relevance."""
        try:
            # Use LLM to rerank results
            messages = [
                {"role": "system", "content": "Rerank these search results by relevance to the query."},
                {"role": "user", "content": f"Query: {query}\nResults: {results}"}
            ]
            reranked_results = await self.llm_adapter.chat(messages)
            
            # Update scores based on reranking
            for i, result in enumerate(results):
                result["score"] = 1.0 - (i / len(results))
            
            return results
            
        except Exception as e:
            logger.warning(f"Reranking failed: {str(e)}, using original results")
            return results

    # Document management methods delegate to vector store
    @hybrid_search_operation
    async def add_document(
        self,
        document: Document,
        **kwargs
    ) -> str:
        """Add a document to the index."""
        return await self.vector_store.add_document(document, **kwargs)

    @hybrid_search_operation
    async def update_document(
        self,
        doc_id: str,
        document: Document,
        **kwargs
    ) -> None:
        """Update a document in the index."""
        await self.vector_store.update_document(doc_id, document, **kwargs)

    @hybrid_search_operation
    async def delete_document(
        self,
        doc_id: str
    ) -> None:
        """Delete a document from the index."""
        await self.vector_store.delete_document(doc_id)

    @hybrid_search_operation
    async def get_document(
        self,
        doc_id: str
    ) -> Optional[Document]:
        """Get a document from the index."""
        return await self.vector_store.get_document(doc_id) 