"""
Retriever Base Classes

This module defines the base classes for document retrieval.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

from ...app.core.types import Document, SearchResult
from ...app.core.metrics import metrics
from ..utils.timeout_retry import hybrid_search_operation


class Retriever(ABC):
    """
    Base class for document retrievers.
    
    This class defines the interface for document retrieval,
    supporting different retrieval strategies (vector, keyword, hybrid).
    """

    def __init__(
        self,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3,
        **kwargs
    ):
        """
        Initialize the retriever.
        
        Args:
            vector_weight: Weight for vector similarity scores (default: 0.7)
            keyword_weight: Weight for keyword match scores (default: 0.3)
            **kwargs: Additional arguments
        """
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight

    @hybrid_search_operation
    async def search(
        self,
        query: str,
        limit: int = 10,
        **kwargs
    ) -> List[SearchResult]:
        """
        Search for relevant documents.
        
        Args:
            query: Search query
            limit: Maximum number of results to return
            **kwargs: Additional arguments
            
        Returns:
            List of search results
        """
        try:
            results = await self._search(query, limit, **kwargs)
            metrics.record_success("retriever_search")
            return results
        except Exception as e:
            metrics.record_failure("retriever_search", str(e))
            raise

    @abstractmethod
    async def _search(
        self,
        query: str,
        limit: int,
        **kwargs
    ) -> List[SearchResult]:
        """
        Implement search logic.
        
        Args:
            query: Search query
            limit: Maximum number of results
            **kwargs: Additional arguments
            
        Returns:
            List of search results
        """
        pass

    @abstractmethod
    async def add_document(
        self,
        document: Document,
        **kwargs
    ) -> str:
        """
        Add a document to the index.
        
        Args:
            document: Document to add
            **kwargs: Additional arguments
            
        Returns:
            Document ID
        """
        pass

    @abstractmethod
    async def update_document(
        self,
        doc_id: str,
        document: Document,
        **kwargs
    ) -> None:
        """
        Update a document in the index.
        
        Args:
            doc_id: Document ID
            document: Updated document
            **kwargs: Additional arguments
        """
        pass

    @abstractmethod
    async def delete_document(
        self,
        doc_id: str
    ) -> None:
        """
        Delete a document from the index.
        
        Args:
            doc_id: Document ID
        """
        pass

    @abstractmethod
    async def get_document(
        self,
        doc_id: str
    ) -> Optional[Document]:
        """
        Get a document from the index.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        pass 