"""
Embedding Factory

This module provides factory functions for creating embedding models with fallback options.
"""

import logging
import os
from typing import Literal

from ...app.config import get_settings
from ...app.core.factory_base import BaseFactory
from .base import EmbeddingModel
from .huggingface import Hugging<PERSON>aceEmbedding
from .openai import OpenAIEmbedding

# Set up logging
logger = logging.getLogger(__name__)


class EmbeddingFactory(BaseFactory[EmbeddingModel]):
    """
    Factory for creating embedding models with automatic fallback options.
    
    This factory creates embedding models with automatic fallback support.
    It maintains a registry of available embedding providers and their
    availability checks.
    """
    
    def _populate_registry(self) -> None:
        """Populate the embedding model registry."""
        # Register providers with availability checks
        self.register_provider(
            "huggingface",
            HuggingFaceEmbedding,
            lambda: True  # Always available as it's the default
        )
        self.register_provider(
            "openai",
            OpenAIEmbedding,
            lambda: "OPENAI_API_KEY" in os.environ
        )
        
        # Set fallback order
        self.set_fallback_order(["huggingface", "openai"])


# Global factory instance
_embedding_factory = EmbeddingFactory()


def get_embedding_model(
    provider: str = None,
    model_name: str = None,
    fallback: bool = True,
    **kwargs
) -> EmbeddingModel:
    """
    Get an embedding model based on configuration or parameters.

    This function reads the configuration from the application settings
    and creates an appropriate embedding model.

    Args:
        provider: Embedding provider (huggingface, openai). If None, uses settings.
        model_name: Model name. If None, uses settings.
        fallback: Whether to enable fallback to other providers
        **kwargs: Additional arguments passed to the embedding model

    Returns:
        An initialized embedding model ready for use
    """
    settings = get_settings()

    # Use provided parameters or fall back to settings
    provider = provider or settings.DEFAULT_EMBEDDING_MODEL
    model_name = model_name or settings.EMBEDDING_MODEL

    logger.info(f"Creating embedding model of type {provider} with model {model_name}")

    # Create the embedding model using the factory
    return _embedding_factory.create(
        provider=provider,
        fallback=fallback,
        model_name=model_name,
        **kwargs
    )