"""
RAG-specific LLM Adapter

This module provides a specialized LLM adapter for RAG operations,
extending the base LLMAdapter with RAG-specific functionality.
"""

from typing import Dict, Any, List, Optional, Union, AsyncIterator, Tuple
import json
try:
    # Try relative imports first
    from ...llm.utils.base import LLMAdapter, ChatMessage
    from ...llm.utils.config import LLMConfig
    from ...llm.utils.factory import get_llm_adapter
    from ...app.core.logging import get_logger
except ImportError:
    # Fallback to absolute imports
    try:
        from backend.llm.utils.base import LLMAdapter, ChatMessage
        from backend.llm.utils.config import LLMConfig
        from backend.llm.utils.factory import get_llm_adapter
        from backend.app.core.logging import get_logger
    except ImportError:
        # Final fallback with minimal implementations
        import logging
        from typing import Dict, Any, List, Optional, Union, AsyncIterator, Tuple

        def get_logger(name):
            return logging.getLogger(name)

        # Import what we can
        try:
            from backend.llm.utils.base import LLMAdapter, ChatMessage
        except ImportError:
            # Minimal fallback implementations
            class ChatMessage(dict):
                pass

            class LLMAdapter:
                async def chat(self, messages, **kwargs):
                    return "Mock response"

        try:
            from backend.llm.utils.config import LLMConfig
        except ImportError:
            class LLMConfig:
                def __init__(self, **kwargs):
                    self.model = kwargs.get('model', 'gpt-4o-mini')

        try:
            from backend.llm.utils.factory import get_llm_adapter
        except ImportError:
            def get_llm_adapter(**kwargs):
                return LLMAdapter()

logger = get_logger(__name__)

class RAGLLMAdapter(LLMAdapter):
    """
    RAG-specific LLM adapter that extends the base LLMAdapter.
    Adds specialized methods for RAG operations while maintaining
    the core LLM functionality from the base class.
    """
    
    def __init__(
        self,
        config: Optional[LLMConfig] = None,
        **kwargs
    ):
        """
        Initialize the RAG LLM adapter.

        Args:
            config: LLM configuration (optional)
            **kwargs: Additional configuration options
        """
        # Store configuration
        self.config = config or {}

        # Initialize underlying LLM adapter using unified factory
        self._underlying_adapter = get_llm_adapter(
            config=config,
            component_type="rag",
            fallback=True,
            **kwargs
        )

        # RAG-specific initialization
        self.analysis_templates = {
            "relevance": """Analyze the relevance of this text to the query:

Text: {text}

Query: {query}

Provide a detailed analysis in JSON format with the following structure:
{{
    "type": "relevance",
    "analysis": {{
        "relevance_score": 0.0,
        "key_points": [],
        "confidence": 0.0,
        "details": {{
            "semantic_match": 0.0,
            "context_match": 0.0,
            "information_density": 0.0
        }}
    }}
}}""",
            
            "quality": """Analyze the quality of this text:

Text: {text}

Provide a detailed analysis in JSON format with the following structure:
{{
    "type": "quality",
    "analysis": {{
        "quality_score": 0.0,
        "key_points": [],
        "confidence": 0.0,
        "details": {{
            "clarity": 0.0,
            "coherence": 0.0,
            "completeness": 0.0,
            "accuracy": 0.0
        }}
    }}
}}""",
            
            "extraction": """Extract key information from this text:

Text: {text}

Extraction Type: {extraction_type}

Provide the extracted information in JSON format with the following structure:
{{
    "type": "{extraction_type}",
    "extracted": {{
        "entities": [],
        "relationships": [],
        "attributes": {{}}
    }}
}}"""
        }

    @property
    def supports_streaming(self) -> bool:
        """Whether the adapter supports streaming responses."""
        return self._underlying_adapter.supports_streaming

    @property
    def model(self) -> str:
        """The model name being used."""
        return self._underlying_adapter.model

    @property
    def client(self):
        """Access to the underlying client instance."""
        return self._underlying_adapter.client

    async def chat(
        self,
        messages: List[ChatMessage],
        **kwargs
    ) -> Union[str, AsyncIterator[str]]:
        """
        Implement chat method from base class.

        Args:
            messages: List of chat messages
            **kwargs: Additional arguments for the chat completion

        Returns:
            The model's response as a string or async iterator
        """
        try:
            # Delegate to underlying adapter
            return await self._underlying_adapter.chat(messages, **kwargs)

        except Exception as e:
            logger.error(f"Error in chat completion: {str(e)}")
            raise
    
    async def get_token_count(
        self,
        messages: List[ChatMessage]
    ) -> Tuple[int, int]:
        """
        Implement token counting from base class.

        Args:
            messages: List of chat messages

        Returns:
            Tuple of (prompt_tokens, completion_tokens)
        """
        try:
            # Delegate to underlying adapter
            return await self._underlying_adapter.get_token_count(messages)

        except Exception as e:
            logger.error(f"Error counting tokens: {str(e)}")
            raise
    
    async def analyze(
        self,
        text: str,
        analysis_type: str,
        query: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Analyze text for specific aspects.
        
        Args:
            text: Text to analyze
            analysis_type: Type of analysis to perform
            query: Optional query for relevance analysis
            **kwargs: Additional arguments for the chat completion
            
        Returns:
            Analysis results as a dictionary
        """
        try:
            # Get template
            template = self.analysis_templates.get(analysis_type)
            if not template:
                raise ValueError(f"Unknown analysis type: {analysis_type}")
            
            # Format prompt
            prompt = template.format(
                text=text,
                query=query or "",
                extraction_type=analysis_type
            )
            
            # Get response
            response = await self.chat([
                {"role": "user", "content": prompt}
            ], **kwargs)
            
            # Parse response
            return self._parse_json_response(response)
            
        except Exception as e:
            logger.error(f"Error in text analysis: {str(e)}")
            raise
    
    async def classify(
        self,
        text: str,
        categories: List[str],
        **kwargs
    ) -> Dict[str, float]:
        """
        Classify text into categories.
        
        Args:
            text: Text to classify
            categories: List of possible categories
            **kwargs: Additional arguments for the chat completion
            
        Returns:
            Dictionary mapping categories to scores
        """
        try:
            # Create classification prompt
            prompt = f"""Classify the following text into the given categories:

Text: {text}

Categories: {', '.join(categories)}

Provide classification scores in JSON format with the following structure:
{{
    "scores": {{
        "category1": 0.0,
        "category2": 0.0,
        ...
    }}
}}"""

            # Get response
            response = await self.chat([
                {"role": "user", "content": prompt}
            ], **kwargs)
            
            # Parse response
            result = self._parse_json_response(response)
            return result.get("scores", {category: 0.0 for category in categories})
            
        except Exception as e:
            logger.error(f"Error in text classification: {str(e)}")
            raise
    
    async def score(
        self,
        text: str,
        criteria: List[str],
        **kwargs
    ) -> Dict[str, float]:
        """
        Score text based on criteria.
        
        Args:
            text: Text to score
            criteria: List of scoring criteria
            **kwargs: Additional arguments for the chat completion
            
        Returns:
            Dictionary mapping criteria to scores
        """
        try:
            # Create scoring prompt
            prompt = f"""Score the following text based on these criteria:

Text: {text}

Criteria: {', '.join(criteria)}

Provide scores in JSON format with the following structure:
{{
    "scores": {{
        "criterion1": 0.0,
        "criterion2": 0.0,
        ...
    }}
}}"""

            # Get response
            response = await self.chat([
                {"role": "user", "content": prompt}
            ], **kwargs)
            
            # Parse response
            result = self._parse_json_response(response)
            return result.get("scores", {criterion: 0.0 for criterion in criteria})
            
        except Exception as e:
            logger.error(f"Error in text scoring: {str(e)}")
            raise
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        Parse JSON response from LLM.
        
        Args:
            response: JSON string response
            
        Returns:
            Parsed JSON as dictionary
        """
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse response as JSON: {response}")
            return {} 