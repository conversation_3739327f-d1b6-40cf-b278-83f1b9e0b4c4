"""
RAG LLM Adapters

This module provides LLM adapters specifically designed for RAG operations.
"""

from typing import Optional
from ..config import rag_settings
from .rag_adapter import RAGLLMAdapter
from ...llm.utils.config import LLMConfig

__all__ = ["RAGLLMAdapter", "get_rag_llm_adapter"]


def get_rag_llm_adapter(config: Optional[LLMConfig] = None) -> RAGLLMAdapter:
    """
    Create a RAG-specific LLM adapter with consistent configuration.

    This function now uses the enhanced configuration system that supports:
    - Environment-driven model selection (RAG_LLM_MODEL)
    - Smart provider detection from model names
    - Component-specific defaults for RAG operations

    Args:
        config: Optional LLM configuration to override defaults

    Returns:
        Configured RAGLLMAdapter instance
    """
    # Use provided config or create RAG-specific config
    if config is None:
        config = LLMConfig(
            component_type="rag",
            temperature=rag_settings.llm.temperature,
            max_tokens=rag_settings.llm.max_tokens,
            timeout=rag_settings.llm.timeout,
            max_retries=rag_settings.llm.max_retries
        )

    return RAGLLMAdapter(config=config)