"""
RAG LLM Adapters

This module provides LLM adapters specifically designed for RAG operations.
"""

from typing import Optional

try:
    # Try relative imports first
    from ..config import rag_settings
    from .rag_adapter import RAGLLMAdapter
    from ...llm.utils.config import LLMConfig
except ImportError:
    # Fallback to absolute imports
    try:
        from backend.rag.config import rag_settings
        from backend.rag.llm.rag_adapter import RAGLLMAdapter
        from backend.llm.utils.config import LLMConfig
    except ImportError:
        # Minimal fallback
        class LLMConfig:
            def __init__(self, **kwargs):
                self.model = kwargs.get('model', 'gpt-4o-mini')
                self.temperature = kwargs.get('temperature', 0.7)
                self.max_tokens = kwargs.get('max_tokens', 1000)
                self.timeout = kwargs.get('timeout', 30.0)
                self.max_retries = kwargs.get('max_retries', 3)

        # Mock rag_settings
        class MockRAGSettings:
            class llm:
                temperature = 0.3
                max_tokens = 1000
                timeout = 60.0
                max_retries = 3

        rag_settings = MockRAGSettings()

        # Import RAGLLMAdapter
        try:
            from backend.rag.llm.rag_adapter import RAGLLMAdapter
        except ImportError:
            from .rag_adapter import RAGLLMAdapter

__all__ = ["RAGLLMAdapter", "get_rag_llm_adapter"]


def get_rag_llm_adapter(config: Optional[LLMConfig] = None) -> RAGLLMAdapter:
    """
    Create a RAG-specific LLM adapter with consistent configuration.

    This function now uses the enhanced configuration system that supports:
    - Environment-driven model selection (RAG_LLM_MODEL)
    - Smart provider detection from model names
    - Component-specific defaults for RAG operations

    Args:
        config: Optional LLM configuration to override defaults

    Returns:
        Configured RAGLLMAdapter instance
    """
    # Use provided config or create RAG-specific config
    if config is None:
        config = LLMConfig(
            component_type="rag",
            temperature=rag_settings.llm.temperature,
            max_tokens=rag_settings.llm.max_tokens,
            timeout=rag_settings.llm.timeout,
            max_retries=rag_settings.llm.max_retries
        )

    return RAGLLMAdapter(config=config)