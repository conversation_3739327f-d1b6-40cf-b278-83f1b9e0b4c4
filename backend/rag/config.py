"""
RAG Configuration

This module provides configuration settings for the RAG system.
"""

import sys
import os
from typing import Optional
from pydantic import Field, BaseModel

# Add current directory to path to help with imports
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

# Define configuration classes locally to avoid import issues
class EmbeddingConfig(BaseModel):
    """Configuration for embedding models."""
    model_name: str = "text-embedding-3-small"
    dimension: int = 1536
    provider: str = "openai"

class VectorStoreConfig(BaseModel):
    """Configuration for vector stores."""
    type: str = "pgvector"
    path: Optional[str] = None
    dimension: int = 1536
    table_name: str = "documents"
    distance_metric: str = "cosine"

class RetrieverConfig(BaseModel):
    """Configuration for retrievers."""
    type: str = "hybrid"
    max_documents: int = 5
    min_score: float = 0.7
    vector_weight: float = 0.7
    keyword_weight: float = 0.3
    use_reranking: bool = False

class ContextConfig(BaseModel):
    """Configuration for context management."""
    max_tokens: int = 2000
    overlap: int = 200
    token_buffer: int = 1000

# Try to import LLMConfig, with fallback
LLMConfig = None
try:
    from backend.llm.utils.config import LLMConfig
except ImportError:
    try:
        from llm.utils.config import LLMConfig
    except ImportError:
        # Fallback LLMConfig implementation
        class LLMConfig:
            """Fallback LLM configuration."""
            def __init__(self, **kwargs):
                self.model = kwargs.get('model', 'gpt-4o-mini')
                self.temperature = kwargs.get('temperature', 0.7)
                self.max_tokens = kwargs.get('max_tokens', 1000)
                self.timeout = kwargs.get('timeout', 30.0)
                self.max_retries = kwargs.get('max_retries', 3)
                self.component_type = kwargs.get('component_type', None)


class RAGSettings(BaseSettings):
    """RAG-specific settings."""
    
    # Embedding settings
    embedding: EmbeddingConfig = Field(
        default_factory=EmbeddingConfig
    )
    
    # Vector store settings
    vector_store: VectorStoreConfig = Field(
        default_factory=VectorStoreConfig
    )
    
    # Retriever settings
    retriever: RetrieverConfig = Field(
        default_factory=RetrieverConfig
    )
    
    # Context settings
    context: ContextConfig = Field(
        default_factory=ContextConfig
    )
    
    # LLM settings
    llm: LLMConfig = Field(
        default_factory=lambda: LLMConfig(
            component_type="rag",
            temperature=0.3,
            max_tokens=1000,
            timeout=60.0,
            max_retries=3
        )
    )
    
    # Timeout settings (in seconds)
    timeout_embedding: float = Field(default=30.0)
    timeout_vector_search: float = Field(default=10.0)
    timeout_keyword_search: float = Field(default=5.0)
    timeout_hybrid_search: float = Field(default=15.0)
    timeout_llm: float = Field(default=60.0)
    
    # Retry settings
    max_retries: int = Field(default=3)
    retry_delay: float = Field(default=1.0)
    retry_jitter: float = Field(default=0.1)
    
    class Config:
        env_prefix = "RAG_"
        case_sensitive = False


# Global instance
rag_settings = RAGSettings() 