"""
RAG Configuration

This module provides configuration settings for the RAG system.
"""

from typing import Optional
from pydantic import Field

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

from ..app.core.types import (
    EmbeddingConfig,
    VectorStoreConfig,
    RetrieverConfig,
    ContextConfig,
)
from ..llm.utils.config import LLMConfig


class RAGSettings(BaseSettings):
    """RAG-specific settings."""
    
    # Embedding settings
    embedding: EmbeddingConfig = Field(
        default_factory=EmbeddingConfig
    )
    
    # Vector store settings
    vector_store: VectorStoreConfig = Field(
        default_factory=VectorStoreConfig
    )
    
    # Retriever settings
    retriever: RetrieverConfig = Field(
        default_factory=RetrieverConfig
    )
    
    # Context settings
    context: ContextConfig = Field(
        default_factory=ContextConfig
    )
    
    # LLM settings
    llm: LLMConfig = Field(
        default_factory=lambda: LLMConfig(
            component_type="rag",
            temperature=0.3,
            max_tokens=1000,
            timeout=60.0,
            max_retries=3
        )
    )
    
    # Timeout settings (in seconds)
    timeout_embedding: float = Field(default=30.0)
    timeout_vector_search: float = Field(default=10.0)
    timeout_keyword_search: float = Field(default=5.0)
    timeout_hybrid_search: float = Field(default=15.0)
    timeout_llm: float = Field(default=60.0)
    
    # Retry settings
    max_retries: int = Field(default=3)
    retry_delay: float = Field(default=1.0)
    retry_jitter: float = Field(default=0.1)
    
    class Config:
        env_prefix = "RAG_"
        case_sensitive = False


# Global instance
rag_settings = RAGSettings() 