"""
Quality Base Classes

This module defines the base classes for quality control.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from ...app.core.logging import get_logger
from ..utils.timeout_retry import hybrid_search_operation

logger = get_logger(__name__)

class BaseQualityComponent(ABC):
    """
    Base class for quality control components.
    
    This class defines the interface for quality control,
    supporting different quality assessment strategies.
    """
    
    def __init__(
        self,
        llm_adapter: Any,
        min_threshold: float = 0.7,
        weights: Optional[Dict[str, float]] = None
    ):
        """
        Initialize quality component.
        
        Args:
            llm_adapter: LLM adapter for scoring
            min_threshold: Minimum quality score threshold
            weights: Optional weights for different quality dimensions
        """
        self.llm_adapter = llm_adapter
        self.min_threshold = min_threshold
        self.weights = weights or {
            "semantic": 0.6,
            "source": 0.4
        }

    @hybrid_search_operation
    @abstractmethod
    async def validate_results(
        self,
        results: List[Dict[str, Any]],
        query: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Validate search results.
        
        Args:
            results: List of search results to validate
            query: Original search query
            
        Returns:
            List of validated results
        """
        pass

    @hybrid_search_operation
    @abstractmethod
    async def score_result(
        self,
        result: Dict[str, Any],
        query: Dict[str, Any]
    ) -> float:
        """
        Score a single result.
        
        Args:
            result: Search result to score
            query: Original search query
            
        Returns:
            Quality score between 0 and 1
        """
        pass

    @hybrid_search_operation
    @abstractmethod
    async def combine_scores(
        self,
        scores: Dict[str, float]
    ) -> float:
        """
        Combine multiple quality scores.
        
        Args:
            scores: Dictionary of dimension scores
            
        Returns:
            Combined quality score
        """
        pass 