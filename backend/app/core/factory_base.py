"""
Base Factory

This module provides a base factory class for creating components with fallback support.
"""

import logging
from typing import Any, Callable, Dict, Generic, Optional, Type, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar('T')

class BaseFactory(Generic[T]):
    """
    Base factory class for creating components with fallback support.
    
    This class provides a common interface for creating components with automatic
    fallback mechanisms. It maintains a registry of available providers and their
    availability checks.
    
    Attributes:
        _registry: Dictionary mapping provider names to their implementation classes
        _availability_checks: Dictionary mapping provider names to their availability check functions
        _fallback_order: List of provider names in order of fallback preference
    """
    
    def __init__(self) -> None:
        """Initialize the factory with empty registries."""
        self._registry: Dict[str, Type[T]] = {}
        self._availability_checks: Dict[str, Callable[[], bool]] = {}
        self._fallback_order: Optional[list[str]] = None
        self._populate_registry()
        
    def _populate_registry(self) -> None:
        """
        Populate the registry with available providers.
        
        This method should be overridden by subclasses to register their specific
        providers and availability checks.
        """
        raise NotImplementedError("Subclasses must implement _populate_registry")
        
    def register_provider(
        self,
        name: str,
        provider_class: Type[T],
        availability_check: Callable[[], bool]
    ) -> None:
        """
        Register a provider with the factory.
        
        Args:
            name: Name of the provider
            provider_class: Class implementing the provider
            availability_check: Function that returns True if the provider is available
        """
        self._registry[name] = provider_class
        self._availability_checks[name] = availability_check
        
    def set_fallback_order(self, order: list[str]) -> None:
        """
        Set the fallback order for providers.
        
        Args:
            order: List of provider names in order of fallback preference
        """
        self._fallback_order = order
        
    def get_registry(self) -> Dict[str, Type[T]]:
        """
        Get the current registry of providers.
        
        Returns:
            Dictionary mapping provider names to their implementation classes
        """
        return self._registry.copy()
        
    def create(
        self,
        provider: str,
        fallback: bool = True,
        **kwargs: Any
    ) -> T:
        """
        Create a component using the specified provider with fallback support.
        
        Args:
            provider: Name of the provider to use
            fallback: Whether to try fallback providers if the requested one fails
            **kwargs: Additional arguments to pass to the provider constructor
            
        Returns:
            An instance of the requested component
            
        Raises:
            ValueError: If the provider is unknown and fallback is False
            ImportError: If the provider is unavailable and fallback is False
        """
        if provider not in self._registry:
            if not fallback:
                raise ValueError(f"Unknown provider: {provider}")
            logger.warning(f"Unknown provider '{provider}', trying fallback providers")
            return self._create_with_fallback(provider, **kwargs)
            
        # Check if the requested provider is available
        if not self._availability_checks[provider]():
            if not fallback:
                raise ImportError(f"Provider '{provider}' is not available")
            logger.warning(
                f"Provider '{provider}' is not available, trying fallback providers"
            )
            return self._create_with_fallback(provider, **kwargs)
            
        # Create the component using the requested provider
        return self._registry[provider](**kwargs)
        
    def _create_with_fallback(self, failed_provider: str, **kwargs: Any) -> T:
        """
        Create a component using fallback providers.
        
        Args:
            failed_provider: Name of the provider that failed
            **kwargs: Additional arguments to pass to the provider constructor
            
        Returns:
            An instance of the requested component
            
        Raises:
            ImportError: If no fallback providers are available
        """
        if not self._fallback_order:
            raise ImportError("No fallback order specified")
            
        # Try each provider in the fallback order
        for provider in self._fallback_order:
            if provider == failed_provider:
                continue
                
            if provider not in self._registry:
                continue
                
            if not self._availability_checks[provider]():
                continue
                
            try:
                return self._registry[provider](**kwargs)
            except Exception as e:
                logger.warning(
                    f"Failed to create component with provider '{provider}': {str(e)}"
                )
                continue
                
        raise ImportError("No available providers found")
