"""
Metrics Collection

This module provides standardized metrics collection and monitoring.
"""

import logging
import time
from collections import defaultdict
from dataclasses import dataclass, field
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class MetricStats:
    """Statistics for a single metric."""
    count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    errors: int = 0
    retries: int = 0
    timeouts: int = 0
    recent_times: List[float] = field(default_factory=list)
    max_recent_times: int = 100  # Keep last 100 measurements


class MetricsCollector:
    """
    Collects and aggregates metrics for monitoring.
    
    This class provides a centralized way to collect and track metrics
    across the application, with support for:
    - Success/failure counts
    - Timing statistics
    - Error tracking
    - Retry monitoring
    - Timeout tracking
    """
    
    def __init__(self):
        """Initialize the metrics collector."""
        self._metrics: Dict[str, MetricStats] = defaultdict(MetricStats)
        
    def record_success(self, metric_name: str, duration: float) -> None:
        """
        Record a successful operation.
        
        Args:
            metric_name: Name of the metric
            duration: Operation duration in seconds
        """
        stats = self._metrics[metric_name]
        stats.count += 1
        stats.total_time += duration
        stats.min_time = min(stats.min_time, duration)
        stats.max_time = max(stats.max_time, duration)
        
        # Update recent times
        stats.recent_times.append(duration)
        if len(stats.recent_times) > stats.max_recent_times:
            stats.recent_times.pop(0)
            
    def record_failure(self, metric_name: str) -> None:
        """
        Record a failed operation.
        
        Args:
            metric_name: Name of the metric
        """
        self._metrics[metric_name].errors += 1
        
    def record_retry(self, metric_name: str) -> None:
        """
        Record a retry attempt.
        
        Args:
            metric_name: Name of the metric
        """
        self._metrics[metric_name].retries += 1
        
    def record_timeout(self, metric_name: str) -> None:
        """
        Record a timeout.
        
        Args:
            metric_name: Name of the metric
        """
        self._metrics[metric_name].timeouts += 1
        
    def get_metric_stats(self, metric_name: str) -> Optional[MetricStats]:
        """
        Get statistics for a metric.
        
        Args:
            metric_name: Name of the metric
            
        Returns:
            MetricStats object if metric exists, None otherwise
        """
        return self._metrics.get(metric_name)
        
    def get_all_metrics(self) -> Dict[str, MetricStats]:
        """
        Get all collected metrics.
        
        Returns:
            Dictionary mapping metric names to their statistics
        """
        return dict(self._metrics)
        
    def log_metrics(self) -> None:
        """Log current metrics to the logger."""
        for name, stats in self._metrics.items():
            if stats.count == 0:
                continue
                
            avg_time = stats.total_time / stats.count
            recent_avg = sum(stats.recent_times) / len(stats.recent_times) if stats.recent_times else 0
            
            logger.info(
                f"Metric '{name}':\n"
                f"  Total operations: {stats.count}\n"
                f"  Success rate: {(stats.count - stats.errors) / stats.count * 100:.1f}%\n"
                f"  Average time: {avg_time:.3f}s\n"
                f"  Recent average: {recent_avg:.3f}s\n"
                f"  Min time: {stats.min_time:.3f}s\n"
                f"  Max time: {stats.max_time:.3f}s\n"
                f"  Errors: {stats.errors}\n"
                f"  Retries: {stats.retries}\n"
                f"  Timeouts: {stats.timeouts}"
            )
            
    def reset_metrics(self) -> None:
        """Reset all collected metrics."""
        self._metrics.clear()


# Global metrics collector instance
metrics = MetricsCollector() 