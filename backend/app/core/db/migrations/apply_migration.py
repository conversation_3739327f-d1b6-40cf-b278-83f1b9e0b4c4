import os
import sys
from pathlib import Path
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent.parent.parent
sys.path.append(str(project_root))

try:
    from backend.app.core.db.database import get_db_connection_params
except ImportError as e:
    print(f"❌ Error importing database module: {str(e)}")
    print("Current Python path:", sys.path)
    sys.exit(1)

def apply_migration():
    """Apply the database migration to add metadata column."""
    try:
        # Get database connection parameters
        db_params = get_db_connection_params()
        
        # Connect to the database
        print("Connecting to database...")
        conn = psycopg2.connect(**db_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cur = conn.cursor()
        
        # Read and execute the migration SQL
        migration_file = Path(__file__).parent / 'add_metadata_to_document_chunks.sql'
        if not migration_file.exists():
            raise FileNotFoundError(f"Migration file not found: {migration_file}")
            
        print("Reading migration file...")
        with open(migration_file, 'r') as f:
            migration_sql = f.read()
            
        print("Applying database migration...")
        cur.execute(migration_sql)
        print("✅ Migration applied successfully!")
        
        # Close the connection
        cur.close()
        conn.close()
        
    except FileNotFoundError as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)
    except psycopg2.Error as e:
        print(f"❌ Database error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    apply_migration() 