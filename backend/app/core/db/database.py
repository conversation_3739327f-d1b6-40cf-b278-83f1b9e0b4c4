"""
Database Connection Management

This module provides database connection management using SQLAlchemy
with connection pooling and context management, supporting both synchronous and asynchronous access.
"""

import logging
from contextlib import contextmanager, asynccontextmanager
from typing import Generator, AsyncGenerator, Optional, ContextManager

import sqlalchemy as sa
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from ...config import get_settings

logger = logging.getLogger(__name__)

# Global database engine and session factory
_engine: Optional[sa.Engine] = None
_session_factory: Optional[sessionmaker] = None

# Global async engine and session factory
_async_engine: Optional[sa.ext.asyncio.AsyncEngine] = None
_async_session_factory: Optional[async_sessionmaker] = None


def init_db() -> None:
    """
    Initialize the database connection and session factory.

    This function creates the SQLAlchemy engine with connection pooling
    and sets up the session factory for database operations.
    """
    global _engine, _session_factory, _async_engine, _async_session_factory
    
    if _engine is not None:
        return

    settings = get_settings()
    
    # Create synchronous engine
    _engine = create_engine(
        settings.DATABASE_URL,
        pool_size=settings.DATABASE_POOL_SIZE,
        max_overflow=settings.DATABASE_MAX_OVERFLOW,
        pool_timeout=settings.DATABASE_POOL_TIMEOUT,
        pool_recycle=settings.DATABASE_POOL_RECYCLE
    )
    
    # Create session factory
    _session_factory = sessionmaker(
        bind=_engine,
        autocommit=False,
        autoflush=False
    )
    
    # Create async engine
    _async_engine = create_async_engine(
        settings.DATABASE_URL,
        pool_size=settings.DATABASE_POOL_SIZE,
        max_overflow=settings.DATABASE_MAX_OVERFLOW,
        pool_timeout=settings.DATABASE_POOL_TIMEOUT,
        pool_recycle=settings.DATABASE_POOL_RECYCLE
    )
    
    # Create async session factory
    _async_session_factory = async_sessionmaker(
        bind=_async_engine,
        autocommit=False,
        autoflush=False
    )


def close_db() -> None:
    """
    Close the database connection and clean up resources.
    """
    global _engine, _session_factory

    if _engine:
        logger.info("Closing database connection")
        _engine.dispose()
        _engine = None
        _session_factory = None


@contextmanager
def get_db_context() -> ContextManager[Session]:
    """
    Get a database session context manager.

    This context manager provides a database session that automatically
    handles transactions and cleanup. It commits on success and rolls back
    on exceptions.

    Yields:
        SQLAlchemy session instance

    Example:
        with get_db_context() as db:
            result = db.execute(text("SELECT * FROM documents"))
            rows = result.fetchall()
    """
    if _engine is None:
        init_db()
    return _session_factory()


def get_engine() -> sa.Engine:
    """
    Get the SQLAlchemy engine instance.

    Returns:
        SQLAlchemy engine

    Raises:
        RuntimeError: If database is not initialized
    """
    if _engine is None:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    return _engine


def check_database_connection() -> bool:
    """
    Check if the database connection is working.

    Returns:
        True if connection is working, False otherwise
    """
    try:
        if _engine is None:
            init_db()

        with _engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


def create_tables() -> None:
    """
    Create database tables if they don't exist.

    This function creates the necessary tables for the RAG system
    including documents and document_chunks tables with vector support.
    """
    if _engine is None:
        init_db()

    logger.info("Creating database tables")

    with get_db_context() as db:
        # Enable pgvector extension first
        db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
        db.commit()  # Commit the extension creation

        # Create documents table
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS documents (
                id VARCHAR(64) PRIMARY KEY,
                content TEXT NOT NULL,
                embedding VECTOR(1536),
                metadata JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """))

        # Create indexes for better performance
        logger.info("Creating database indexes")

        # Vector similarity index (IVFFlat for compatibility)
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS documents_embedding_idx 
            ON documents 
            USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100)
        """))

        # Full-text search index
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS documents_content_fts
            ON documents USING gin(to_tsvector('english', content))
        """))

        # Metadata index
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS documents_metadata_idx
            ON documents USING gin(metadata)
        """))

        db.commit()
        logger.info("Database tables and indexes created successfully")


def drop_tables(confirm: bool = False) -> None:
    """
    Drop all database tables.

    Args:
        confirm: If True, drop tables without confirmation
    """
    if not confirm:
        logger.warning("Drop tables not confirmed. Set confirm=True to proceed.")
        return

    if _engine is None:
        init_db()

    logger.info("Dropping database tables")

    with get_db_context() as db:
        db.execute(text("DROP TABLE IF EXISTS document_chunks CASCADE"))
        db.execute(text("DROP TABLE IF EXISTS documents CASCADE"))


def reset_database(confirm: bool = False) -> None:
    """
    Reset the database by dropping and recreating all tables.

    Args:
        confirm: If True, reset database without confirmation
    """
    if not confirm:
        logger.warning("Reset database not confirmed. Set confirm=True to proceed.")
        return

    drop_tables(confirm=True)
    create_tables()


def get_table_info() -> dict:
    """
    Get information about database tables.

    Returns:
        Dictionary with table information
    """
    if _engine is None:
        init_db()

    with get_db_context() as db:
        # Get list of tables
        result = db.execute(text("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
        """))
        tables = [row[0] for row in result]

        # Get column information for each table
        table_info = {}
        for table in tables:
            result = db.execute(text(f"""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = '{table}'
            """))
            columns = [
                {
                    "name": row[0],
                    "type": row[1],
                    "nullable": row[2] == "YES"
                }
                for row in result
            ]
            table_info[table] = columns

        return table_info


def ensure_pgvector_extension() -> None:
    """
    Ensure the pgvector extension is installed in the database.
    """
    if _engine is None:
        init_db()

    with get_db_context() as db:
        db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
        db.commit()
        logger.info("pgvector extension ensured")


def get_db_connection_params() -> dict:
    """
    Get database connection parameters from settings.

    Returns:
        Dictionary with database connection parameters
    """
    settings = get_settings()
    
    # Parse the DATABASE_URL to get individual components
    from urllib.parse import urlparse
    parsed = urlparse(settings.DATABASE_URL)
    
    return {
        'dbname': parsed.path[1:],  # Remove leading slash
        'user': parsed.username,
        'password': parsed.password,
        'host': parsed.hostname,
        'port': parsed.port or 5432
    }


# --- Async Database Support ---

async def init_async_db() -> None:
    """
    Initialize the async database connection and session factory.
    """
    global _async_engine, _async_session_factory

    settings = get_settings()

    logger.info("Initializing async database connection")

    # Create async engine with connection pooling
    _async_engine = create_async_engine(
        settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
        echo=settings.DEBUG,
        future=True
    )

    # Create async session factory
    _async_session_factory = async_sessionmaker(
        bind=_async_engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        future=True
    )

    # Test the connection
    try:
        async with _async_engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
        logger.info("Async database connection established successfully")
    except Exception as e:
        logger.error(f"Failed to connect to async database: {e}")
        raise


async def close_async_db() -> None:
    """
    Close the async database connection and clean up resources.
    """
    global _async_engine, _async_session_factory

    if _async_engine:
        logger.info("Closing async database connection")
        await _async_engine.dispose()
        _async_engine = None
        _async_session_factory = None


@asynccontextmanager
async def get_async_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get an async database session context manager.

    This context manager provides an async database session that automatically
    handles transactions and cleanup. It commits on success and rolls back
    on exceptions.

    Yields:
        Async SQLAlchemy session instance

    Example:
        async with get_async_db_session() as db:
            result = await db.execute(text("SELECT * FROM documents"))
            rows = result.fetchall()
    """
    if _async_session_factory is None:
        await init_async_db()

    session = _async_session_factory()
    try:
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        logger.error(f"Async database transaction failed: {e}")
        raise
    finally:
        await session.close()


async def get_async_engine() -> sa.ext.asyncio.AsyncEngine:
    """
    Get the async SQLAlchemy engine instance.

    Returns:
        Async SQLAlchemy engine

    Raises:
        RuntimeError: If async database is not initialized
    """
    if _async_engine is None:
        raise RuntimeError("Async database not initialized. Call init_async_db() first.")
    return _async_engine


async def check_async_database_connection() -> bool:
    """
    Check if the async database connection is working.

    Returns:
        True if connection is working, False otherwise
    """
    try:
        if _async_engine is None:
            await init_async_db()

        async with _async_engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"Async database connection check failed: {e}")
        return False
