"""
Configuration Management

This module provides centralized configuration management using Pydantic BaseSettings.
It handles environment variable loading, validation, and provides default values.
"""

import os
from functools import lru_cache
from typing import Optional, List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    Uses Pydantic BaseSettings for automatic environment variable loading,
    type validation, and default value management.
    """

    # Core Application Settings
    DEBUG: bool = Field(default=False, description="Enable debug mode")
    DEVELOPMENT_MODE: bool = Field(default=False, description="Enable development mode")
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    PORT: int = Field(default=8000, description="Server port")

    # Database Settings
    DATABASE_URL: str = Field(
        default="postgresql://postgres:postgres@localhost:5432/businesslm",
        description="PostgreSQL database URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=5, description="Database connection pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=10, description="Database max overflow connections")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout in seconds")
    DATABASE_POOL_RECYCLE: int = Field(default=1800, description="Database pool recycle time in seconds")

    # LLM API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, description="Google API key")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, description="HuggingFace API key")

    # LangSmith Settings (Optional)
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="Enable LangSmith tracing")
    LANGCHAIN_API_KEY: Optional[str] = Field(default=None, description="LangSmith API key")
    LANGCHAIN_PROJECT: str = Field(default="businesslm-poc", description="LangSmith project name")

    # LLM Default Settings
    LLM_DEFAULT_MODEL: str = Field(default="gpt-4o-mini", description="Default LLM model for system-wide use")
    LLM_DEFAULT_TEMPERATURE: float = Field(default=0.7, description="Default LLM temperature")
    LLM_DEFAULT_MAX_TOKENS: int = Field(default=1000, description="Default LLM max tokens")
    LLM_DEFAULT_MAX_RETRIES: int = Field(default=3, description="Default LLM max retries")
    LLM_DEFAULT_TIMEOUT: float = Field(default=30.0, description="Default LLM timeout in seconds")

    # Component-specific LLM model overrides
    RAG_LLM_MODEL: Optional[str] = Field(default=None, description="LLM model specifically for RAG operations")
    AGENT_LLM_MODEL: Optional[str] = Field(default=None, description="LLM model specifically for agent operations")
    ORCHESTRATION_LLM_MODEL: Optional[str] = Field(default=None, description="LLM model specifically for orchestration")
    PLANNING_LLM_MODEL: Optional[str] = Field(default=None, description="LLM model specifically for planning operations")

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"LOG_LEVEL must be one of {allowed_levels}")
        return v.upper()

    def validate_api_keys(self) -> List[str]:
        """
        Validate that at least one LLM API key is provided.

        Returns:
            List of missing required configurations
        """
        errors = []

        # Check if at least one LLM API key is provided
        llm_keys = [self.OPENAI_API_KEY, self.ANTHROPIC_API_KEY, self.GOOGLE_API_KEY]
        if not any(llm_keys):
            errors.append("At least one LLM API key must be provided (OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_API_KEY)")

        # Check database URL format
        if not self.DATABASE_URL.startswith("postgresql://"):
            errors.append("DATABASE_URL must be a valid PostgreSQL connection string")

        return errors

    def validate_llm_models(self) -> List[str]:
        """
        Validate LLM model configuration.

        Returns:
            List of validation error messages
        """
        errors = []

        # Import here to avoid circular imports
        try:
            from ..llm.utils.model_registry import validate_model

            # Validate default model
            is_valid, error_msg = validate_model(self.LLM_DEFAULT_MODEL)
            if not is_valid:
                errors.append(f"LLM_DEFAULT_MODEL: {error_msg}")
            elif error_msg:  # Warning message
                errors.append(f"LLM_DEFAULT_MODEL: {error_msg}")

            # Validate component-specific models if provided
            component_models = {
                "RAG_LLM_MODEL": self.RAG_LLM_MODEL,
                "AGENT_LLM_MODEL": self.AGENT_LLM_MODEL,
                "ORCHESTRATION_LLM_MODEL": self.ORCHESTRATION_LLM_MODEL,
                "PLANNING_LLM_MODEL": self.PLANNING_LLM_MODEL,
            }

            for config_name, model_name in component_models.items():
                if model_name:  # Only validate if provided
                    is_valid, error_msg = validate_model(model_name)
                    if not is_valid:
                        errors.append(f"{config_name}: {error_msg}")
                    elif error_msg:  # Warning message
                        errors.append(f"{config_name}: {error_msg}")

        except ImportError as e:
            errors.append(f"Could not validate LLM models: {e}")

        return errors

    class Config:
        """Pydantic configuration."""
        env_file = ".env"  # Look for .env in project root
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields (frontend variables, etc.)


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.

    Uses lru_cache to ensure settings are loaded only once and cached
    for subsequent calls, improving performance.

    Returns:
        Settings instance with loaded configuration
    """
    settings = Settings()

    # Validate configuration in development mode
    if settings.DEVELOPMENT_MODE:
        # Validate API keys
        api_key_errors = settings.validate_api_keys()
        if api_key_errors:
            import warnings
            for error in api_key_errors:
                warnings.warn(f"API Configuration warning: {error}")

        # Validate LLM models
        llm_model_errors = settings.validate_llm_models()
        if llm_model_errors:
            import warnings
            for error in llm_model_errors:
                warnings.warn(f"LLM Model Configuration warning: {error}")

    return settings


# Global settings instance for easy import
settings = get_settings()
